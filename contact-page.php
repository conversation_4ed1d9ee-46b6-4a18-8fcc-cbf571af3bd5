<?php include 'header.php'; ?>

<!-- Contact Hero Section -->
<section id="modern-hero" class="modern-hero-section">
    <div class="hero-background-wrapper">
        <div class="hero-background-image">
            <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" alt="Contact Eldergrove Energy" class="hero-bg-img">
        </div>
        <div class="hero-gradient-overlay"></div>
        <div class="hero-particles"></div>
    </div>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 text-center">
                <div class="hero-content-modern">
                    <div class="hero-badge-modern">
                        <i class="fas fa-envelope"></i>
                        <span>Contact Us</span>
                    </div>
                    <h1 class="hero-title-modern contact-hero-title">
                        Let's Build a <span class="text-gradient-modern">Sustainable</span><br>
                        Future Together
                    </h1>
                    <p class="hero-description-modern">
                        Ready to partner with us on your renewable energy journey? Our global team of experts 
                        is here to help you achieve your sustainability goals with innovative clean energy solutions.
                    </p>
                    <div class="hero-actions-modern">
                        <a href="#contact-form" class="btn-modern-primary">
                            <span>Get in Touch</span>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                        <a href="#global-offices" class="btn-modern-outline">
                            <span>Find an Office</span>
                        </a>
                    </div>
                    <div class="hero-scroll-indicator">
                        <div class="scroll-arrow">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section 1: Contact Form -->
<section id="contact-form" class="contact-form-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-6">
                <div class="contact-form-content">
                    <div class="section-badge-modern">
                        <i class="fas fa-paper-plane"></i>
                        <span>Send Message</span>
                    </div>
                    <h2 class="section-title-modern contact-title-smaller">
                        Start Your <span class="text-accent">Renewable Energy</span><br>
                        Journey Today
                    </h2>
                    <p class="contact-form-description">
                        Get in touch with our renewable energy experts. We're here to help you find the perfect
                        sustainable energy solution for your needs.
                    </p>
                    <div class="contact-info-modern">
                        <div class="contact-info-item">
                            <div class="contact-info-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-info-details">
                                <h4>Phone</h4>
                                <p>+44 20 7946 0958</p>
                                <span>24/7 Support Available</span>
                            </div>
                        </div>
                        <div class="contact-info-item">
                            <div class="contact-info-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-info-details">
                                <h4>Email</h4>
                                <p><EMAIL></p>
                                <span>Response within 24 hours</span>
                            </div>
                        </div>
                        <div class="contact-info-item">
                            <div class="contact-info-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-info-details">
                                <h4>Address</h4>
                                <p>25 Canada Square, Canary Wharf<br>London E14 5LQ, UK</p>
                                <span>Global Headquarters</span>
                            </div>
                        </div>
                        <div class="contact-info-item">
                            <div class="contact-info-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-info-details">
                                <h4>Business Hours</h4>
                                <p>Monday - Friday: 9:00 AM - 5:30 PM GMT</p>
                                <span>Emergency support available 24/7</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="contact-form-wrapper-modern">
                    <form class="contact-form-modern" action="contact.php" method="POST">
                        <div class="form-row">
                            <div class="form-group-half">
                                <label for="name">Full Name *</label>
                                <input type="text" id="name" name="name" required>
                            </div>
                            <div class="form-group-half">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group-half">
                                <label for="company">Company</label>
                                <input type="text" id="company" name="company">
                            </div>
                            <div class="form-group-half">
                                <label for="subject">Service Interest</label>
                                <select id="subject" name="subject">
                                    <option value="">Select service</option>
                                    <option value="Wind Energy">Wind Energy</option>
                                    <option value="Solar Power">Solar Power</option>
                                    <option value="Energy Storage">Energy Storage</option>
                                    <option value="General Inquiry">General Inquiry</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="message">Message *</label>
                            <textarea id="message" name="message" rows="4" required placeholder="Tell us about your project..."></textarea>
                        </div>
                        <!-- Honeypot field for spam protection -->
                        <input type="text" name="website" style="display: none;">
                        <div class="form-group">
                            <button type="submit" class="btn-contact-submit-modern">
                                <span>Send Message</span>
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section 4: FAQ & Support -->
<section class="faq-support-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-6">
                <div class="faq-content">
                    <div class="section-badge-modern">
                        <i class="fas fa-question-circle"></i>
                        <span>Frequently Asked Questions</span>
                    </div>
                    <h2 class="section-title-modern">
                        Common <span class="text-accent">Questions</span><br>
                        About Our Services
                    </h2>
                    <div class="faq-list">
                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>What renewable energy solutions do you offer?</h4>
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="faq-answer">
                                <p>We provide comprehensive renewable energy solutions including wind energy, solar power, energy storage, biomass, hydroelectric, geothermal, and smart grid technologies. Our services span from initial feasibility studies to long-term operations and maintenance.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>How long does a typical renewable energy project take?</h4>
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="faq-answer">
                                <p>Project timelines vary depending on technology, scale, and location. Typically, wind projects take 3-5 years from development to operation, solar projects 1-3 years, and energy storage projects 6-18 months. We provide detailed timelines during the consultation phase.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>Do you provide financing options?</h4>
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="faq-answer">
                                <p>Yes, we offer various financing solutions including power purchase agreements (PPAs), equipment financing, and partnerships with leading financial institutions. Our team can help structure the optimal financing approach for your project.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>What is your global project experience?</h4>
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="faq-answer">
                                <p>We have successfully delivered over 15.2GW of renewable energy capacity across 35+ countries. Our portfolio includes some of the world's largest offshore wind farms, utility-scale solar installations, and innovative energy storage systems.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="support-content">
                    <div class="section-badge-modern">
                        <i class="fas fa-question-circle"></i>
                        <span>More FAQs</span>
                    </div>
                    <h2 class="section-title-modern">
                        Additional <span class="text-accent">Questions</span><br>
                        & Answers
                    </h2>
                    <div class="faq-list">
                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>What maintenance services do you provide?</h4>
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="faq-answer">
                                <p>We offer comprehensive operations and maintenance services including preventive maintenance, performance monitoring, component replacement, and 24/7 technical support to ensure optimal performance of your renewable energy systems.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>Do you provide energy storage solutions?</h4>
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="faq-answer">
                                <p>Yes, we design and install advanced battery energy storage systems that provide grid stability, peak shaving, backup power, and renewable energy smoothing. Our solutions range from utility-scale to residential applications.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>What is your warranty and support policy?</h4>
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="faq-answer">
                                <p>We provide comprehensive warranties on all equipment and workmanship, typically ranging from 10-25 years depending on the technology. Our support includes remote monitoring, on-site maintenance, and guaranteed performance metrics.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>How do you ensure project quality and safety?</h4>
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="faq-answer">
                                <p>We maintain strict quality and safety standards certified by international bodies. Our projects undergo rigorous testing, regular inspections, and comply with all local and international safety regulations and environmental standards.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>Can you help with permits and regulatory approvals?</h4>
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="faq-answer">
                                <p>Absolutely. Our experienced team handles all aspects of permitting and regulatory compliance, working closely with local authorities to ensure your project meets all requirements and proceeds smoothly through approval processes.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Mobile App-Style Contact Page (Hidden on Desktop) -->
<div class="mobile-contact-wrapper">
    <!-- Mobile Hero Section -->
    <section class="mobile-hero-main">
        <div class="mobile-hero-bg">
            <div class="mobile-hero-overlay"></div>
        </div>
        <div class="mobile-hero-content">
            <div class="mobile-hero-badge">
                <i class="fas fa-envelope"></i>
                <span>Contact Us</span>
            </div>
            <h1 class="mobile-hero-title">
                Let's Build a <span class="mobile-text-gradient">Sustainable</span><br>
                Future Together
            </h1>
            <p class="mobile-hero-description">
                Ready to partner with us on your renewable energy journey? Our global team of experts 
                is here to help you achieve your sustainability goals.
            </p>
            <div class="mobile-hero-actions">
                <a href="#mobile-contact-form" class="mobile-btn-primary">
                    <span>Get in Touch</span>
                    <i class="fas fa-arrow-down"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Quick Contact Cards -->
    <section class="mobile-quick-contact">
        <div class="mobile-contact-cards">
            <a href="tel:+442079460958" class="mobile-contact-card call-card">
                <div class="card-icon">
                    <i class="fas fa-phone"></i>
                </div>
                <div class="card-content">
                    <h3>Call Us</h3>
                    <p>+44 20 7946 0958</p>
                </div>
                <div class="card-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </a>
            
            <a href="mailto:<EMAIL>" class="mobile-contact-card email-card">
                <div class="card-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="card-content">
                    <h3>Email Us</h3>
                    <p><EMAIL></p>
                </div>
                <div class="card-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </a>
            
            <div class="mobile-contact-card location-card">
                <div class="card-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="card-content">
                    <h3>Visit Us</h3>
                    <p>London, United Kingdom</p>
                </div>
                <div class="card-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Contact Form -->
    <section id="mobile-contact-form" class="mobile-contact-form">
        <div class="mobile-form-header">
            <h2>Send us a message</h2>
            <p>We'll get back to you within 24 hours</p>
        </div>
        
        <form class="mobile-form" action="contact.php" method="POST">
            <div class="mobile-form-group">
                <label for="mobile-name">Your Name</label>
                <input type="text" id="mobile-name" name="name" required>
            </div>
            
            <div class="mobile-form-group">
                <label for="mobile-email">Email Address</label>
                <input type="email" id="mobile-email" name="email" required>
            </div>
            
            <div class="mobile-form-group">
                <label for="mobile-company">Company (Optional)</label>
                <input type="text" id="mobile-company" name="company">
            </div>
            
            <div class="mobile-form-group">
                <label for="mobile-service">Service Interest</label>
                <select id="mobile-service" name="subject">
                    <option value="">Choose a service</option>
                    <option value="Wind Energy">Wind Energy</option>
                    <option value="Solar Power">Solar Power</option>
                    <option value="Energy Storage">Energy Storage</option>
                    <option value="General Inquiry">General Inquiry</option>
                </select>
            </div>
            
            <div class="mobile-form-group">
                <label for="mobile-message">Your Message</label>
                <textarea id="mobile-message" name="message" rows="4" required placeholder="Tell us about your project..."></textarea>
            </div>
            
            <!-- Honeypot -->
            <input type="text" name="website" style="display: none;">
            
            <button type="submit" class="mobile-submit-btn">
                <span>Send Message</span>
                <i class="fas fa-arrow-right"></i>
            </button>
        </form>
    </section>

    <!-- Mobile FAQ Section -->
    <section class="mobile-faq">
        <div class="mobile-faq-header">
            <h2>Quick Answers</h2>
            <p>Common questions about our services</p>
        </div>
        
        <div class="mobile-faq-list">
            <div class="mobile-faq-item">
                <div class="mobile-faq-question">
                    <span>What services do you offer?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="mobile-faq-answer">
                    <p>We provide wind energy, solar power, energy storage, and comprehensive renewable energy solutions.</p>
                </div>
            </div>
            
            <div class="mobile-faq-item">
                <div class="mobile-faq-question">
                    <span>How long do projects take?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="mobile-faq-answer">
                    <p>Project timelines vary: wind projects 3-5 years, solar 1-3 years, energy storage 6-18 months.</p>
                </div>
            </div>
            
            <div class="mobile-faq-item">
                <div class="mobile-faq-question">
                    <span>Do you offer financing?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="mobile-faq-answer">
                    <p>Yes, we offer PPAs, equipment financing, and partnerships with financial institutions.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Business Hours -->
    <section class="mobile-hours">
        <div class="mobile-hours-content">
            <div class="hours-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="hours-info">
                <h3>Business Hours</h3>
                <p>Monday - Friday: 9:00 AM - 5:30 PM GMT</p>
                <span>Emergency support available 24/7</span>
            </div>
        </div>
    </section>
</div>

<!-- Contact Page Styles -->
<style>
/* Contact Form Section */
.contact-form-section {
    padding: 6rem 0;
    background: #f8fafc;
}

.contact-title-smaller {
    font-size: 2rem !important;
}

.contact-hero-title {
    font-size: 3.5rem !important;
    line-height: 1.2 !important;
    font-weight: 800 !important;
}

.contact-form-description {
    font-size: 1.125rem;
    color: #64748b;
    line-height: 1.7;
    margin-bottom: 3rem;
}

.contact-info-modern {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-info-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-info-details h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.contact-info-details p {
    color: #1e293b;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.contact-info-details span {
    color: #64748b;
    font-size: 0.9rem;
}

.contact-form-wrapper-modern {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
}

.contact-form-modern .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.contact-form-modern .form-group-half {
    flex: 1;
}

.contact-form-modern .form-group {
    margin-bottom: 1rem;
}

.contact-form-modern label {
    display: block;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.contact-form-modern input,
.contact-form-modern select,
.contact-form-modern textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: white;
}

.contact-form-modern input:focus,
.contact-form-modern select:focus,
.contact-form-modern textarea:focus {
    outline: none;
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.btn-contact-submit-modern {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.btn-contact-submit-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
}

/* Global Offices Section */
.global-offices-section {
    padding: 6rem 0;
    background: white;
}

.offices-header {
    margin-bottom: 4rem;
}

.office-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
    transition: all 0.3s ease;
    height: 100%;
}

.office-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.featured-office {
    border: 2px solid #22c55e;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.featured-office .office-title {
    color: #22c55e;
}

.office-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f1f5f9;
}

.office-flag {
    font-size: 2rem;
}

.office-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.office-location {
    color: #64748b;
    font-weight: 500;
}

.office-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.office-address,
.office-contact,
.office-hours {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.office-address i,
.office-contact i,
.office-hours i {
    color: #22c55e;
    font-size: 1.1rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.contact-item:last-child {
    margin-bottom: 0;
}

/* FAQ & Support Section */
.faq-support-section {
    padding: 6rem 0;
    background: white;
}

.faq-list {
    margin-top: 2rem;
}

.faq-item {
    border: 1px solid #f1f5f9;
    border-radius: 12px;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: #22c55e;
}

.faq-question {
    padding: 1.5rem;
    background: #f8fafc;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: #f1f5f9;
}

.faq-question h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0;
}

.faq-question i {
    color: #22c55e;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 0;
}

.support-resources {
    margin-top: 2rem;
}

.resource-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 2rem;
    background: #f8fafc;
    border-radius: 15px;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.resource-item:hover {
    background: #f1f5f9;
    transform: translateY(-2px);
}

.resource-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
    box-shadow: 0 8px 20px rgba(34, 197, 94, 0.3);
}

.resource-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.resource-content p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.resource-link {
    color: #22c55e;
    font-weight: 600;
    text-decoration: none;
    transition: all .3s ease;
}

.resource-link:hover {
    color: #16a34a;
    transform: translateX(5px);
    text-decoration: none;
}

/* Mobile App-Style Enhancements for Contact Page */
@media (max-width: 768px) {
    .contact-form-section {
        padding: 4rem 0 !important; /* Increased padding for better spacing */
    }
    
    .contact-card {
        border-radius: 25px; /* Increased rounding for softer app-like appearance */
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15); /* Stronger shadow for depth */
        margin-bottom: 2rem; /* Slightly increased margin */
        transition: all 0.3s ease;
    }
    
    .contact-form-wrapper-modern {
        background: white;
        padding: 2.5rem;
        border-radius: 20px;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        border: 2px solid #22c55e;
    }
    
    .contact-form-modern .form-row {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
    }
    
    .contact-form-modern .form-group-half {
        flex: 1;
    }
    
    .contact-form-modern .form-group {
        margin-bottom: 1.5rem;
    }
    
    .contact-form-modern label {
        display: block;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.75rem;
        font-size: 1rem;
    }
    
    .contact-form-modern input,
    .contact-form-modern select,
    .contact-form-modern textarea {
        width: 100%;
        padding: 1rem;
        border: 2px solid #22c55e;
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8fafc;
    }
    
    .contact-form-modern input:focus,
    .contact-form-modern select:focus,
    .contact-form-modern textarea:focus {
        outline: none;
        border-color: #16a34a;
        box-shadow: 0 0 0 5px rgba(22, 163, 74, 0.2);
    }
    
    .btn-contact-submit-modern {
        background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
        color: white;
        padding: 1rem 2rem;
        border: none;
        border-radius: 12px;
        font-weight: 700;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        box-shadow: 0 8px 25px rgba(22, 163, 74, 0.3);
    }
    
    .btn-contact-submit-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(22, 163, 74, 0.4);
    }
    
    /* Enhanced mobile interactions */
    .contact-form-modern input, .contact-form-modern select, .contact-form-modern textarea {
        font-size: 1.1rem;
        padding: 1.2rem;
    }
    
    /* Simplified layout for app-like experience */
    .col-lg-6 {
        flex: 100%;
    }
    
    /* Floating Action Button for Mobile */
    .floating-chat-btn {
        position: fixed;
        bottom: 90px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        text-decoration: none;
        box-shadow: 0 10px 25px rgba(22, 163, 74, 0.4);
        z-index: 999;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% {
            box-shadow: 0 10px 25px rgba(22, 163, 34, 0.4);
        }
        50% {
            box-shadow: 0 10px 25px rgba(22, 163, 34, 0.6);
        }
        100% {
            box-shadow: 0 10px 25px rgba(22, 163, 34, 0.4);
        }
    }
}

/* Hide desktop contact page on mobile and show mobile version */
@media (max-width: 768px) {
    /* Hide desktop sections on mobile */
    .modern-hero-section,
    .contact-form-section,
    .faq-support-section {
        display: none !important;
    }
    
    /* Show mobile contact wrapper */
    .mobile-contact-wrapper {
        display: block !important;
    }
}

@media (min-width: 769px) {
    /* Hide mobile contact wrapper on desktop */
    .mobile-contact-wrapper {
        display: none !important;
    }
}

/* Mobile Contact Styles */
.mobile-contact-wrapper {
    display: none; /* Hidden by default, shown on mobile */
    background: #f8fafc;
    min-height: 100vh;
}

/* Mobile Hero Main Section */
.mobile-hero-main {
    position: relative;
    min-height: 70vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.mobile-hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    background-image: url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.mobile-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(22, 163, 74, 0.8) 100%);
}

.mobile-hero-content {
    position: relative;
    z-index: 2;
    padding: 2rem 1rem;
    text-align: center;
    color: white;
    width: 100%;
}

.mobile-hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1.25rem;
    border-radius: 50px;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.mobile-hero-badge i {
    font-size: 1rem;
}

.mobile-hero-badge span {
    font-weight: 600;
    font-size: 0.9rem;
}

.mobile-hero-title {
    font-size: 2.25rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.mobile-text-gradient {
    background: linear-gradient(135deg, #fff 0%, #f0fdf4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.mobile-hero-description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.95;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
    color: white;
}

.mobile-hero-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.mobile-btn-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 1rem 2rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.mobile-btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.mobile-quick-contact {
    padding: 1.5rem 1rem;
}

.mobile-contact-cards {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mobile-contact-card {
    background: white;
    padding: 1.25rem;
    border-radius: 16px;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.mobile-contact-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    text-decoration: none;
    color: inherit;
}

.mobile-contact-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.call-card .card-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.email-card .card-icon {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.location-card .card-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.mobile-contact-card .card-content {
    flex: 1;
}

.mobile-contact-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.mobile-contact-card p {
    color: #64748b;
    font-size: 0.9rem;
    margin-bottom: 0;
}

.mobile-contact-card .card-arrow {
    color: #cbd5e1;
    font-size: 0.9rem;
}

.mobile-contact-form {
    padding: 2rem 1rem;
    background: white;
    margin: 0 1rem;
    border-radius: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.mobile-form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.mobile-form-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.mobile-form-header p {
    color: #64748b;
    font-size: 0.95rem;
}

.mobile-form-group {
    margin-bottom: 1.5rem;
}

.mobile-form-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.mobile-form-group input,
.mobile-form-group select,
.mobile-form-group textarea {
    width: 100%;
    padding: 0.875rem;
    border: 2px solid #f1f5f9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.mobile-form-group input:focus,
.mobile-form-group select:focus,
.mobile-form-group textarea:focus {
    outline: none;
    border-color: #22c55e;
    background: white;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.mobile-submit-btn {
    width: 100%;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
    padding: 1rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.mobile-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
}

.mobile-faq {
    padding: 2rem 1rem;
}

.mobile-faq-header {
    text-align: center;
    margin-bottom: 2rem;
}

.mobile-faq-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.mobile-faq-header p {
    color: #64748b;
    font-size: 0.95rem;
}

.mobile-faq-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mobile-faq-item {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.mobile-faq-question {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.mobile-faq-question:hover {
    background: #f8fafc;
}

.mobile-faq-question span {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.95rem;
}

.mobile-faq-question i {
    color: #22c55e;
    transition: transform 0.3s ease;
}

.mobile-faq-item.active .mobile-faq-question i {
    transform: rotate(180deg);
}

.mobile-faq-answer {
    padding: 0 1rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.mobile-faq-item.active .mobile-faq-answer {
    padding: 1rem;
    max-height: 150px;
}

.mobile-faq-answer p {
    color: #64748b;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 0;
}

.mobile-hours {
    padding: 1.5rem 1rem;
    margin: 0 1rem 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.mobile-hours-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.hours-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.hours-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.hours-info p {
    color: #1e293b;
    font-weight: 500;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.hours-info span {
    color: #64748b;
    font-size: 0.85rem;
}

/* General Styles */
body {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    color: #1e293b;
}

h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
    color: #1e293b;
}

p {
    margin: 0;
    padding: 0;
    color: #64748b;
    line-height: 1.6;
}

a {
    color: #22c55e;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #16a34a;
}

button {
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
    font: inherit;
}

/* Utilities */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.5rem;
}

.col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 0.5rem;
}

.col-lg-10 {
    flex: 0 0 83.3333%;
    max-width: 83.3333%;
    padding: 0 0.5rem;
}

.text-center {
    text-align: center;
}

.justify-content-center {
    justify-content: center;
}

.section-badge-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #f1f5f9;
    padding: 0.5rem 1rem;
    border-radius: 16px;
    margin-bottom: 1.5rem;
}

.section-badge-modern i {
    color: #22c55e;
    font-size: 1.2rem;
}

.text-gradient-modern {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.btn-modern-primary {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.btn-modern-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
}

.btn-modern-outline {
    background: none;
    color: #22c55e;
    padding: 0.875rem 1.75rem;
    border: 2px solid #22c55e;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-modern-outline:hover {
    background: #f8fafc;
}

/* Mobile App Styles */
.mobile-contact-wrapper {
    display: none;
}

@media (max-width: 768px) {
    .mobile-contact-wrapper {
        display: block;
        padding: 0 1rem;
    }
    
    .mobile-contact-hero {
        background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
        color: white;
        padding: 4rem 0;
        border-radius: 20px 20px 0 0;
        position: relative;
        overflow: hidden;
    }
    
    .mobile-hero-icon {
        position: absolute;
        top: -20px;
        right: 20px;
        font-size: 4rem;
        opacity: 0.15;
    }
    
    .mobile-hero-content {
        position: relative;
        z-index: 1;
        text-align: center;
    }
    
    .mobile-quick-contact {
        margin-top: 2rem;
    }
    
    .mobile-contact-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .mobile-contact-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }
    
    .card-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        flex-shrink: 0;
    }
    
    .card-content h3 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }
    
    .card-content p {
        color: #64748b;
        font-size: 0.9rem;
        margin-bottom: 0;
    }
    
    .mobile-contact-form {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-top: 2rem;
    }
    
    .mobile-form-header {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .mobile-form-header h2 {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.5rem;
    }
    
    .mobile-form-header p {
        color: #64748b;
        font-size: 0.9rem;
        margin-bottom: 0;
    }
    
    .mobile-form-group {
        margin-bottom: 1.5rem;
    }
    
    .mobile-form-group label {
        display: block;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }
    
    .mobile-form-group input,
    .mobile-form-group select,
    .mobile-form-group textarea {
        width: 100%;
        padding: 0.875rem;
        border: 2px solid #f1f5f9;
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8fafc;
    }
    
    .mobile-form-group input:focus,
    .mobile-form-group select:focus,
    .mobile-form-group textarea:focus {
        outline: none;
        border-color: #22c55e;
        background: white;
        box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
    }
    
    .mobile-submit-btn {
        width: 100%;
        background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
        color: white;
        padding: 1rem;
        border: none;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
    }
    
    .mobile-submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
    }
    
    .mobile-faq {
        padding: 2rem 1rem;
    }
    
    .mobile-faq-header {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .mobile-faq-header h2 {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.5rem;
    }
    
    .mobile-faq-header p {
        color: #64748b;
        font-size: 0.9rem;
        margin-bottom: 0;
    }
    
    .mobile-faq-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .mobile-faq-item {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        overflow: hidden;
    }
    
    .mobile-faq-question {
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }
    
    .mobile-faq-question:hover {
        background: #f8fafc;
    }
    
    .mobile-faq-question span {
        font-weight: 600;
        color: #1e293b;
        font-size: 0.95rem;
    }
    
    .mobile-faq-question i {
        color: #22c55e;
        transition: transform 0.3s ease;
    }
    
    .mobile-faq-item.active .mobile-faq-question i {
        transform: rotate(180deg);
    }
    
    .mobile-faq-answer {
        padding: 0 1rem;
        max-height: 0;
        overflow: hidden;
        transition: all 0.3s ease;
        background: #f8fafc;
    }
    
    .mobile-faq-item.active .mobile-faq-answer {
        padding: 1rem;
        max-height: 150px;
    }
    
    .mobile-faq-answer p {
        color: #64748b;
        font-size: 0.9rem;
        line-height: 1.6;
        margin-bottom: 0;
    }
    
    .mobile-hours {
        padding: 1.5rem 1rem;
        margin: 0 1rem 2rem;
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    }
    
    .mobile-hours-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .hours-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        flex-shrink: 0;
    }
    
    .hours-info h3 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }
    
    .hours-info p {
        color: #1e293b;
        font-weight: 500;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
    }
    
    .hours-info span {
        color: #64748b;
        font-size: 0.85rem;
    }
}

/* Floating Action Button for Live Chat */
.floating-chat-btn {
    display: none !important;
}

/* Form Message Styles */
.form-message {
    margin: 1.5rem 0;
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    animation: slideIn 0.3s ease-out;
    transition: opacity 0.3s ease;
}

.form-success {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    border: 1px solid #22c55e;
    color: #15803d;
}

.form-error {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    border: 1px solid #ef4444;
    color: #dc2626;
}

.success-icon, .error-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.success-icon i {
    color: #22c55e;
}

.error-icon i {
    color: #ef4444;
}

.success-content h4, .error-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.success-content p, .error-content p {
    margin: 0;
    line-height: 1.5;
}

.error-content ul {
    margin: 0.5rem 0 0 0;
    padding-left: 1.25rem;
}

.error-content li {
    margin-bottom: 0.25rem;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading state for submit buttons */
.btn-contact-submit-modern:disabled,
.mobile-submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
}

.btn-contact-submit-modern:disabled:hover,
.mobile-submit-btn:disabled:hover {
    transform: none !important;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3) !important;
}

/* Mobile form message styles */
@media (max-width: 768px) {
    .form-message {
        margin: 1rem 0;
        padding: 1.25rem;
        border-radius: 16px;
    }

    .success-content h4, .error-content h4 {
        font-size: 1rem;
    }

    .success-content p, .error-content p {
        font-size: 0.9rem;
    }
}
</style>



<script>
// FAQ Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');

        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');

            // Close all FAQ items
            faqItems.forEach(faq => faq.classList.remove('active'));

            // Open clicked item if it wasn't active
            if (!isActive) {
                item.classList.add('active');
            }
        });
    });
});

// Mobile FAQ toggle functionality
document.querySelectorAll('.mobile-faq-question').forEach(question => {
    question.addEventListener('click', function() {
        const faqItem = this.parentElement;
        const isActive = faqItem.classList.contains('active');
        
        // Close all other FAQ items
        document.querySelectorAll('.mobile-faq-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Toggle current item
        if (!isActive) {
            faqItem.classList.add('active');
        }
        
        // Haptic feedback on mobile
        if (window.innerWidth <= 768 && navigator.vibrate) {
            navigator.vibrate(30);
        }
    });
});

// Add haptic feedback for mobile interactions
if ('vibrate' in navigator) {
    document.querySelectorAll('.btn, .contact-card, .faq-question, .floating-chat-btn').forEach(element => {
        element.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                navigator.vibrate(50);
            }
        });
    });
}

// Mobile form enhancements
document.querySelectorAll('.mobile-form input, .mobile-form select, .mobile-form textarea').forEach(input => {
    input.addEventListener('focus', function() {
        if (window.innerWidth <= 768) {
            this.style.transform = 'scale(1.02)';
            if (navigator.vibrate) {
                navigator.vibrate(20);
            }
        }
    });
    
    input.addEventListener('blur', function() {
        if (window.innerWidth <= 768) {
            this.style.transform = 'scale(1)';
        }
    });
});

// Mobile contact card haptic feedback
document.querySelectorAll('.mobile-contact-card').forEach(card => {
    card.addEventListener('click', function() {
        if (window.innerWidth <= 768 && navigator.vibrate) {
            navigator.vibrate(40);
        }
    });
});

// Smooth scrolling for mobile hero button
document.querySelector('.mobile-btn-primary')?.addEventListener('click', function(e) {
    e.preventDefault();
    const target = document.querySelector('#mobile-contact-form');
    if (target) {
        target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
        if (window.innerWidth <= 768 && navigator.vibrate) {
            navigator.vibrate(50);
        }
    }
});
</script>

<?php include 'footer.php'; ?>
