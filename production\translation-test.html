<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation System Test - Eldergrove Energy</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8fafc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            border-left: 4px solid #22c55e;
        }
        .test-title {
            color: #22c55e;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .back-link {
            display: inline-block;
            background: #22c55e;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .back-link:hover {
            background: #16a34a;
            color: white;
        }
    </style>
</head>
<body>
    <a href="index.php" class="back-link">← Back to Home</a>
    
    <h1>Translation System Test Page</h1>
    
    <div class="test-section">
        <div class="test-title">English Content Test</div>
        <p>This page contains various types of content to test the translation system functionality. The custom language switcher should translate all text content on this page while maintaining the professional design.</p>
    </div>

    <div class="test-section">
        <div class="test-title">Technical Terms</div>
        <p>Renewable energy solutions including solar panels, wind turbines, biomass conversion, energy storage systems, smart grid technology, and sustainable power generation.</p>
    </div>

    <div class="test-section">
        <div class="test-title">Business Content</div>
        <p>Eldergrove Energy is a leading global provider of renewable energy solutions. We specialize in wind energy, solar power systems, energy storage, biomass energy, smart grid technology, and energy consulting services.</p>
    </div>

    <div class="test-section">
        <div class="test-title">Contact Information</div>
        <p>Contact <NAME_EMAIL> or call +44-20-7946-0958. Our office is located at 25 Canada Square, Canary Wharf, London E14 5LQ, UK.</p>
    </div>

    <div class="test-section">
        <div class="test-title">Numbers and Dates</div>
        <p>Founded in 1971, we have over 50 years of experience. We have completed more than 1,000 projects worldwide, generating over 10,000 MW of clean energy.</p>
    </div>

    <div class="test-section">
        <div class="test-title">Special Characters</div>
        <p>Testing special characters: áéíóú, ñ, ç, ü, ß, æ, ø, å, and various symbols: €, £, $, ¥, ©, ®, ™</p>
    </div>

    <script>
        // Test script to verify translation functionality
        console.log('Translation test page loaded');
        
        // Monitor for translation changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    console.log('Content changed - translation may be active');
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });

        // Test language detection
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page language:', document.documentElement.lang);
            console.log('Current URL:', window.location.href);
            
            // Check if Google Translate elements are present
            setTimeout(() => {
                const googleElements = document.querySelectorAll('[class*="goog-te"]');
                console.log('Google Translate elements found:', googleElements.length);
                
                if (googleElements.length > 0) {
                    console.log('Translation system is active');
                } else {
                    console.log('Translation system may not be loaded yet');
                }
            }, 2000);
        });
    </script>
</body>
</html>
