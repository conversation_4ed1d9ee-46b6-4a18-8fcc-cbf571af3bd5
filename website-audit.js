const { chromium } = require('playwright');

async function auditWebsite() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  const page = await context.newPage();

  const baseUrl = 'http://localhost/eldergroveenergy.com';
  const pages = [
    { name: 'Homepage', url: `${baseUrl}/index.php` },
    { name: 'About', url: `${baseUrl}/about.php` },
    { name: 'Services', url: `${baseUrl}/services.php` },
    { name: 'Projects', url: `${baseUrl}/projects.php` },
    { name: 'Team', url: `${baseUrl}/team.php` },
    { name: 'Technology', url: `${baseUrl}/technology.php` },
    { name: 'Sustainability', url: `${baseUrl}/sustainability.php` },
    { name: 'Careers', url: `${baseUrl}/careers.php` },
    { name: 'Contact', url: `${baseUrl}/contact.php` }
  ];

  const auditResults = [];

  for (const pageInfo of pages) {
    console.log(`\n🔍 Auditing ${pageInfo.name}...`);
    
    try {
      // Navigate to page and measure load time
      const startTime = Date.now();
      await page.goto(pageInfo.url, { waitUntil: 'networkidle' });
      const loadTime = Date.now() - startTime;

      // Basic page info
      const title = await page.title();
      const url = page.url();
      
      // Check for common UI elements
      const hasHeader = await page.locator('header').count() > 0;
      const hasNav = await page.locator('nav').count() > 0;
      const hasFooter = await page.locator('footer').count() > 0;
      const hasLogo = await page.locator('img[alt*="logo"], .logo').count() > 0;
      
      // Check for forms
      const forms = await page.locator('form').count();
      const inputs = await page.locator('input').count();
      
      // Check for images and their alt text
      const images = await page.locator('img').count();
      const imagesWithAlt = await page.locator('img[alt]').count();
      const imagesWithoutAlt = images - imagesWithAlt;
      
      // Check for links
      const links = await page.locator('a').count();
      const externalLinks = await page.locator('a[href^="http"]').count();
      
      // Check for responsive design indicators
      const hasViewportMeta = await page.locator('meta[name="viewport"]').count() > 0;
      
      // Check for accessibility features
      const hasSkipLinks = await page.locator('a[href^="#"]').count() > 0;
      const hasHeadings = await page.locator('h1, h2, h3, h4, h5, h6').count();
      
      // Check console errors
      const errors = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });

      // Wait a bit to capture any console errors
      await page.waitForTimeout(2000);

      const result = {
        page: pageInfo.name,
        url: pageInfo.url,
        status: 'success',
        loadTime: `${loadTime}ms`,
        title,
        structure: {
          hasHeader,
          hasNav,
          hasFooter,
          hasLogo,
          hasViewportMeta
        },
        content: {
          headings: hasHeadings,
          images: `${images} total, ${imagesWithoutAlt} missing alt text`,
          links: `${links} total, ${externalLinks} external`,
          forms: forms > 0 ? `${forms} forms with ${inputs} inputs` : 'No forms'
        },
        accessibility: {
          hasSkipLinks,
          imagesWithAlt: `${imagesWithAlt}/${images}`,
          headingStructure: hasHeadings > 0
        },
        errors: errors.length > 0 ? errors : 'None detected'
      };

      auditResults.push(result);
      console.log(`✅ ${pageInfo.name} audit complete`);
      
    } catch (error) {
      auditResults.push({
        page: pageInfo.name,
        url: pageInfo.url,
        status: 'error',
        error: error.message
      });
      console.log(`❌ ${pageInfo.name} audit failed: ${error.message}`);
    }
  }

  // Mobile responsiveness test
  console.log('\n📱 Testing mobile responsiveness...');
  await context.setViewportSize({ width: 375, height: 667 }); // iPhone size
  
  try {
    await page.goto(`${baseUrl}/index.php`);
    await page.waitForTimeout(2000);
    
    const mobileResults = {
      viewport: '375x667 (Mobile)',
      hasHamburgerMenu: await page.locator('.hamburger, .menu-toggle, .mobile-menu-trigger').count() > 0,
      hasResponsiveNav: await page.locator('nav').isVisible(),
      textOverflow: await page.evaluate(() => {
        const elements = document.querySelectorAll('*');
        let overflowCount = 0;
        elements.forEach(el => {
          if (el.scrollWidth > el.clientWidth) overflowCount++;
        });
        return overflowCount;
      })
    };
    
    auditResults.push({
      page: 'Mobile Test',
      ...mobileResults
    });
  } catch (error) {
    console.log(`❌ Mobile test failed: ${error.message}`);
  }

  await browser.close();

  // Generate report
  console.log('\n' + '='.repeat(60));
  console.log('📊 ELDER GROVE ENERGY WEBSITE AUDIT REPORT');
  console.log('='.repeat(60));
  
  auditResults.forEach(result => {
    console.log(`\n🔸 ${result.page.toUpperCase()}`);
    console.log(`   URL: ${result.url || 'N/A'}`);
    
    if (result.status === 'error') {
      console.log(`   ❌ ERROR: ${result.error}`);
      return;
    }
    
    if (result.loadTime) {
      console.log(`   ⏱️  Load Time: ${result.loadTime}`);
      console.log(`   📄 Title: ${result.title}`);
    }
    
    if (result.structure) {
      console.log(`   🏗️  Structure: ${Object.entries(result.structure).map(([k,v]) => `${k}: ${v}`).join(', ')}`);
    }
    
    if (result.content) {
      console.log(`   📝 Content: ${Object.entries(result.content).map(([k,v]) => `${k}: ${v}`).join(', ')}`);
    }
    
    if (result.accessibility) {
      console.log(`   ♿ Accessibility: ${Object.entries(result.accessibility).map(([k,v]) => `${k}: ${v}`).join(', ')}`);
    }
    
    if (result.errors && result.errors !== 'None detected') {
      console.log(`   ⚠️  Errors: ${Array.isArray(result.errors) ? result.errors.join(', ') : result.errors}`);
    }
    
    if (result.hasHamburgerMenu !== undefined) {
      console.log(`   📱 Mobile: Hamburger menu: ${result.hasHamburgerMenu}, Responsive nav: ${result.hasResponsiveNav}, Text overflow issues: ${result.textOverflow}`);
    }
  });

  console.log('\n' + '='.repeat(60));
  console.log('🎯 RECOMMENDATIONS');
  console.log('='.repeat(60));
  
  // Generate recommendations based on findings
  const recommendations = [];
  
  auditResults.forEach(result => {
    if (result.content && result.content.images.includes('missing alt text')) {
      const missing = result.content.images.match(/(\d+) missing alt text/);
      if (missing && parseInt(missing[1]) > 0) {
        recommendations.push(`${result.page}: Add alt text to ${missing[1]} images for better accessibility`);
      }
    }
    
    if (result.structure && !result.structure.hasViewportMeta) {
      recommendations.push(`${result.page}: Add viewport meta tag for mobile responsiveness`);
    }
    
    if (result.loadTime && parseInt(result.loadTime) > 3000) {
      recommendations.push(`${result.page}: Optimize page load time (currently ${result.loadTime})`);
    }
    
    if (result.errors && Array.isArray(result.errors) && result.errors.length > 0) {
      recommendations.push(`${result.page}: Fix console errors: ${result.errors.slice(0, 2).join(', ')}`);
    }
  });
  
  if (recommendations.length === 0) {
    console.log('✅ No major issues found! Your website looks good.');
  } else {
    recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });
  }
  
  console.log('\n🚀 Audit complete!');
}

auditWebsite().catch(console.error);
