<?php
/**
 * Enhanced Contact Form Handler for Eldergrove Energy
 * Supports both PHP mail() and SMTP sending
 */

// Configuration
$config = [
    'to_email' => '<EMAIL>',
    'test_email' => '<EMAIL>', // For testing purposes
    'from_name' => 'Eldergrove Energy Website',
    'from_email' => '<EMAIL>', // Use your domain email
    'use_smtp' => false, // Set to true to use SMTP instead of mail()
    'log_submissions' => true,
    'enable_debug' => true // Set to true for development/testing
];

// Enable error reporting for development only
if ($config['enable_debug']) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set content type
header('Content-Type: application/json');

// Allow CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Response array
$response = array();

// Check if form was submitted via POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    // Sanitize and validate input data
    $name = isset($_POST['name']) ? trim(htmlspecialchars($_POST['name'], ENT_QUOTES, 'UTF-8')) : '';
    $email = isset($_POST['email']) ? trim(strtolower($_POST['email'])) : '';
    $company = isset($_POST['company']) ? trim(htmlspecialchars($_POST['company'], ENT_QUOTES, 'UTF-8')) : '';
    $subject = isset($_POST['subject']) ? trim(htmlspecialchars($_POST['subject'], ENT_QUOTES, 'UTF-8')) : '';
    $message = isset($_POST['message']) ? trim(htmlspecialchars($_POST['message'], ENT_QUOTES, 'UTF-8')) : '';

    // Validation
    $errors = array();

    // Name validation
    if (empty($name)) {
        $errors[] = 'Name is required';
    } elseif (strlen($name) < 2) {
        $errors[] = 'Name must be at least 2 characters long';
    } elseif (strlen($name) > 100) {
        $errors[] = 'Name must be less than 100 characters';
    }

    // Email validation
    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    } elseif (strlen($email) > 254) {
        $errors[] = 'Email address is too long';
    }

    // Subject validation
    if (empty($subject)) {
        $errors[] = 'Service interest is required';
    }

    // Message validation
    if (empty($message)) {
        $errors[] = 'Message is required';
    } elseif (strlen($message) < 10) {
        $errors[] = 'Message must be at least 10 characters long';
    } elseif (strlen($message) > 5000) {
        $errors[] = 'Message must be less than 5000 characters';
    }

    // Anti-spam measures
    $honeypot = isset($_POST['website']) ? $_POST['website'] : '';
    if (!empty($honeypot)) {
        $errors[] = 'Spam detected';
    }

    // Rate limiting (simple session-based)
    session_start();
    $current_time = time();
    $last_submission = isset($_SESSION['last_contact_submission']) ? $_SESSION['last_contact_submission'] : 0;

    if (($current_time - $last_submission) < 60) { // 1 minute cooldown
        $errors[] = 'Please wait before sending another message';
    }

    // If no errors, process the form
    if (empty($errors)) {

        // Update last submission time
        $_SESSION['last_contact_submission'] = $current_time;

        // Prepare email content
        $email_subject = 'New Contact Form Submission: ' . $subject;

        // Create HTML email body
        $email_body_html = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
                .content { background: #f8fafc; padding: 20px; border-radius: 0 0 8px 8px; }
                .field { margin-bottom: 15px; }
                .label { font-weight: bold; color: #1e293b; }
                .value { margin-top: 5px; padding: 10px; background: white; border-radius: 4px; border-left: 4px solid #22c55e; }
                .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0; font-size: 12px; color: #64748b; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>New Contact Form Submission</h2>
                    <p>Eldergrove Energy Website</p>
                </div>
                <div class='content'>
                    <div class='field'>
                        <div class='label'>Name:</div>
                        <div class='value'>" . htmlspecialchars($name) . "</div>
                    </div>
                    <div class='field'>
                        <div class='label'>Email:</div>
                        <div class='value'>" . htmlspecialchars($email) . "</div>
                    </div>
                    " . (!empty($company) ? "
                    <div class='field'>
                        <div class='label'>Company:</div>
                        <div class='value'>" . htmlspecialchars($company) . "</div>
                    </div>
                    " : "") . "
                    <div class='field'>
                        <div class='label'>Service Interest:</div>
                        <div class='value'>" . htmlspecialchars($subject) . "</div>
                    </div>
                    <div class='field'>
                        <div class='label'>Message:</div>
                        <div class='value'>" . nl2br(htmlspecialchars($message)) . "</div>
                    </div>
                    <div class='footer'>
                        <p><strong>Submission Details:</strong></p>
                        <p>Date: " . date('Y-m-d H:i:s T') . "</p>
                        <p>IP Address: " . $_SERVER['REMOTE_ADDR'] . "</p>
                        <p>User Agent: " . htmlspecialchars($_SERVER['HTTP_USER_AGENT']) . "</p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        ";

        // Create plain text version
        $email_body_text = "
New contact form submission from Eldergrove Energy website:

Name: $name
Email: $email" . (!empty($company) ? "\nCompany: $company" : "") . "
Service Interest: $subject

Message:
$message

---
Submitted on: " . date('Y-m-d H:i:s T') . "
IP Address: " . $_SERVER['REMOTE_ADDR'] . "
User Agent: " . $_SERVER['HTTP_USER_AGENT'] . "
        ";

        $email_sent = false;

        if ($config['use_smtp']) {
            // SMTP sending (requires PHPMailer)
            $email_sent = sendEmailSMTP($config, $email_subject, $email_body_html, $email_body_text, $email, $name);
        } else {
            // Use PHP mail() function
            $email_sent = sendEmailPHP($config, $email_subject, $email_body_html, $email_body_text, $email, $name);
        }

        if ($email_sent) {
            // Log successful submission
            if ($config['log_submissions']) {
                $log_entry = date('Y-m-d H:i:s') . " - SUCCESS - Contact form submission from: $name ($email) - Subject: $subject\n";
                file_put_contents('contact_log.txt', $log_entry, FILE_APPEND | LOCK_EX);
            }

            $response['success'] = true;
            $response['message'] = 'Thank you for your message! We\'ll get back to you within 24 hours.';

        } else {
            // Log failed submission
            if ($config['log_submissions']) {
                $log_entry = date('Y-m-d H:i:s') . " - FAILED - Contact form submission from: $name ($email) - Subject: $subject\n";
                file_put_contents('contact_log.txt', $log_entry, FILE_APPEND | LOCK_EX);
            }

            $response['success'] = false;
            $response['message'] = 'Sorry, there was an error sending your message. Please try again later or contact us directly at ' . $config['to_email'];
        }

    } else {
        $response['success'] = false;
        $response['message'] = 'Please correct the following errors:';
        $response['errors'] = $errors;
    }

} else {
    $response['success'] = false;
    $response['message'] = 'Invalid request method';
}

// Return JSON response
echo json_encode($response);

/**
 * Send email using PHP mail() function
 */
function sendEmailPHP($config, $subject, $html_body, $text_body, $reply_email, $reply_name) {
    $to = $config['to_email'];

    // Create proper headers for HTML email
    $headers = array();
    $headers[] = 'MIME-Version: 1.0';
    $headers[] = 'Content-Type: text/html; charset=UTF-8';
    $headers[] = 'From: ' . $config['from_name'] . ' <' . $config['from_email'] . '>';
    $headers[] = 'Reply-To: ' . $reply_name . ' <' . $reply_email . '>';
    $headers[] = 'X-Mailer: PHP/' . phpversion();
    $headers[] = 'X-Priority: 3';

    // Send email
    return mail($to, $subject, $html_body, implode("\r\n", $headers));
}

/**
 * Send email using SMTP (requires PHPMailer)
 */
function sendEmailSMTP($config, $subject, $html_body, $text_body, $reply_email, $reply_name) {
    // This function would use PHPMailer for SMTP
    // For now, fall back to PHP mail
    return sendEmailPHP($config, $subject, $html_body, $text_body, $reply_email, $reply_name);
}

// Alternative: If you want to redirect back to the contact page with a message
// You can uncomment the following and comment out the JSON response above

/*
if (isset($response['success']) && $response['success']) {
    header('Location: index.html#contact?success=1');
} else {
    header('Location: index.html#contact?error=1');
}
exit;
*/
?>
