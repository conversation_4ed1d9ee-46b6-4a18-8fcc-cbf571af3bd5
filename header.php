<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eldergrove - Leading the Global Renewable Energy Revolution</title>

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍃</text></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍃</text></svg>">

    <!-- SEO Meta Tags -->
    <meta name="description" content="Eldergrove Energy - Leading global provider of renewable energy solutions including wind, solar, biomass, and energy storage. Powering a sustainable future since 1971.">
    <meta name="keywords" content="renewable energy, wind power, solar energy, biomass, energy storage, offshore wind, onshore wind, sustainable energy, clean energy, green technology">
    <meta name="author" content="Eldergrove Energy">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">
    <meta name="revisit-after" content="7 days">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Eldergrove Energy - Renewable Energy Solutions">
    <meta property="og:description" content="Leading global provider of renewable energy solutions including wind, solar, biomass, and energy storage. Powering a sustainable future since 1971.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://eldergroveenergy.com">
    <meta property="og:image" content="https://eldergroveenergy.com/assets/images/logos/logo.png">
    <meta property="og:site_name" content="Eldergrove Energy">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Eldergrove Energy - Renewable Energy Solutions">
    <meta name="twitter:description" content="Leading global provider of renewable energy solutions including wind, solar, biomass, and energy storage.">
    <meta name="twitter:image" content="https://eldergroveenergy.com/assets/images/logos/logo.png">

    <!-- Performance Optimization -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//images.unsplash.com">
    <link rel="dns-prefetch" href="//translate.google.com">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/slider.css">

    <!-- Logo Styles -->
    <style>
        .brand-logo-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .brand-logo-img {
            height: 50px;
            width: auto;
            transition: all 0.3s ease;
        }

        /* Mobile Navigation Improvements */
        .mobile-menu-header {
            display: flex;
            justify-content: flex-end;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .mobile-close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #64748b;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .mobile-close-btn:hover {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        /* Mobile logo sizing */
        @media (max-width: 768px) {
            .brand-logo-img {
                height: 58px;
            }

            /* Mobile menu styling */
            .navbar-collapse {
                background: white;
                border-radius: 0 0 15px 15px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                margin-top: 1rem;
            }

            /* Mobile menu dividers */
            .navbar-nav .nav-item {
                border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            }

            .navbar-nav .nav-item:last-child {
                border-bottom: none;
            }

            .navbar-nav .nav-link {
                padding: 1rem 1.5rem;
                color: #1e293b;
                font-weight: 500;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .navbar-nav .nav-link:hover {
                background: rgba(34, 197, 94, 0.1);
                color: #22c55e;
                padding-left: 2rem;
            }

            /* Google Translate widget in mobile */
            #google_translate_element {
                padding: 1rem 1.5rem;
            }

            /* Mobile app-like animations */
            .navbar-nav .nav-link {
                position: relative;
                overflow: hidden;
            }

            /* Smooth hamburger animation */
            .navbar-toggler {
                border: none;
                padding: 0.5rem;
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .navbar-toggler:focus {
                box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
            }

            .navbar-toggler-icon {
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2834, 197, 94, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
            }

            /* Mobile menu slide animation */
            .navbar-collapse.collapsing,
            .navbar-collapse.show {
                animation: slideDown 0.3s ease-out;
            }

            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        }

        .navbar-brand:hover .brand-logo-img {
            transform: scale(1.1);
        }

        .brand-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            letter-spacing: -0.025em;
        }

        .navbar-brand:hover .brand-text {
            color: #22c55e;
        }

        @media (max-width: 768px) {
            .brand-logo-img {
                height: 32px;
            }

            .brand-text {
                font-size: 1.25rem;
            }
        }

        /* Google Translate Styles */
        .google-translate-widget {
            margin: 0 10px;
            min-width: 120px;
            height: 38px;
            display: flex;
            align-items: center;
        }

        .goog-te-gadget {
            font-family: inherit !important;
            font-size: 0 !important;
            width: 100% !important;
        }

        .goog-te-gadget .goog-te-combo {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 10px 15px;
            font-size: 14px;
            color: #1e293b;
            outline: none;
            transition: all 0.3s ease;
            width: 100% !important;
            min-width: 130px;
            height: 42px;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .goog-te-gadget .goog-te-combo:hover,
        .goog-te-gadget .goog-te-combo:focus {
            border-color: #22c55e;
            background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.15);
            transform: translateY(-1px);
        }

        /* Style Google Translate dropdown menu */
        .goog-te-menu-frame {
            border-radius: 12px !important;
            border: 2px solid #22c55e !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
            overflow: hidden !important;
        }

        .goog-te-menu2 {
            background: white !important;
            border: none !important;
            border-radius: 12px !important;
        }

        .goog-te-menu2-item {
            padding: 12px 16px !important;
            font-family: 'Inter', sans-serif !important;
            font-size: 14px !important;
            color: #1e293b !important;
            transition: all 0.2s ease !important;
            border-bottom: 1px solid #f1f5f9 !important;
        }

        .goog-te-menu2-item:hover {
            background: #f0fdf4 !important;
            color: #22c55e !important;
        }

        .goog-te-menu2-item-selected {
            background: #22c55e !important;
            color: white !important;
        }

        .goog-te-banner-frame {
            display: none !important;
        }

        /* Hide all Google Translate banners and notifications */
        .goog-te-banner-frame.skiptranslate {
            display: none !important;
        }

        .goog-te-ftab {
            display: none !important;
        }

        .goog-te-balloon-frame {
            display: none !important;
        }

        body {
            top: 0 !important;
        }

        /* Fix navigation layout shifts */
        .navbar-nav {
            align-items: center;
        }

        .nav-item {
            display: flex;
            align-items: center;
        }

        /* Prevent layout shifts during Google Translate loading */
        #google_translate_element:empty {
            min-width: 120px;
            height: 38px;
        }

        @media (max-width: 768px) {
            .google-translate-widget {
                margin: 10px 0;
                min-width: 100px;
            }

            .goog-te-gadget .goog-te-combo {
                min-width: 100px;
            }
        }

        /* Performance Optimizations */
        img {
            loading: lazy;
        }

        /* Critical CSS for above-the-fold content */
        .navbar {
            will-change: transform;
        }

        .hero-slide-bg-img,
        .hero-bg-img {
            object-fit: cover;
            object-position: center;
        }

        /* Reduce layout shifts */
        .container {
            max-width: 1200px;
        }

        /* Optimize animations */
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
    </style>

    <!-- Google Translate Script -->
    <script type="text/javascript">
        function googleTranslateElementInit() {
            new google.translate.TranslateElement({
                pageLanguage: 'en',
                includedLanguages: 'en,de,fr,ru,es,zh,ja,hi,ar,pt',
                layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
                autoDisplay: false
            }, 'google_translate_element');

            // Auto-hide translation banners
            setTimeout(function() {
                const banners = document.querySelectorAll('.goog-te-banner-frame, .goog-te-ftab, .goog-te-balloon-frame');
                banners.forEach(banner => {
                    banner.style.display = 'none';
                });

                // Reset body top position
                document.body.style.top = '0';
                document.body.style.position = 'static';
            }, 1000);
        }

        // Additional banner hiding on page load
        document.addEventListener('DOMContentLoaded', function() {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) {
                            const banners = node.querySelectorAll ? node.querySelectorAll('.goog-te-banner-frame, .goog-te-ftab, .goog-te-balloon-frame') : [];
                            banners.forEach(banner => {
                                banner.style.display = 'none';
                            });

                            if (node.classList && (node.classList.contains('goog-te-banner-frame') || node.classList.contains('goog-te-ftab') || node.classList.contains('goog-te-balloon-frame'))) {
                                node.style.display = 'none';
                            }
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });
    </script>
    <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Eldergrove Energy",
        "url": "https://eldergroveenergy.com",
        "logo": "https://eldergroveenergy.com/assets/images/logos/logo.png",
        "description": "Leading global provider of renewable energy solutions including wind, solar, biomass, and energy storage.",
        "foundingDate": "1971",
        "industry": "Renewable Energy",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "25 Canada Square, Canary Wharf",
            "addressLocality": "London",
            "postalCode": "E14 5LQ",
            "addressCountry": "UK"
        },
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+44-20-7946-0958",
            "contactType": "customer service",
            "email": "<EMAIL>"
        },

        "services": [
            "Wind Energy Solutions",
            "Solar Power Systems",
            "Energy Storage",
            "Biomass Energy",
            "Smart Grid Technology",
            "Energy Consulting"
        ]
    }
    </script>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header-nav fixed-top">
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <!-- Brand Logo -->
                <a class="navbar-brand" href="index.php">
                    <img src="assets/images/logos/logo.png" alt="Eldergrove Energy" class="brand-logo-img">
                </a>

                <!-- Mobile Toggle Button -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Navigation Menu -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <!-- Mobile Close Button -->
                    <div class="mobile-menu-header d-lg-none">
                        <button type="button" class="mobile-close-btn" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="about.php">About</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="team.php">Team</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link offcanvas-trigger" href="#" data-bs-toggle="offcanvas" data-bs-target="#servicesOffcanvas">
                                Services
                                <i class="fas fa-chevron-right ms-1"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="projects.php">Projects</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="technology.php">Technology</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="sustainability.php">Sustainability</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="contact-page.php">Contact</a>
                        </li>
                        <li class="nav-item">
                            <div id="google_translate_element" class="google-translate-widget"></div>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Off-canvas Services Menu -->
    <div class="offcanvas offcanvas-end services-offcanvas" tabindex="-1" id="servicesOffcanvas" aria-labelledby="servicesOffcanvasLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="servicesOffcanvasLabel">
                <i class="fas fa-cogs me-2"></i>
                Our Services
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <div class="services-menu-header">
                <h6>Renewable Energy Solutions</h6>
                <p>Comprehensive energy technologies for a sustainable future</p>
            </div>

            <div class="services-menu-section">
                <h6 class="section-title">Primary Technologies</h6>
                <div class="services-menu-links">
                    <a href="service-detail.php?service=wind-energy" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-wind"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Wind Energy</strong>
                            <span>Advanced onshore and offshore wind solutions</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="service-detail.php?service=solar-power" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-solar-panel"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Solar Power</strong>
                            <span>Utility-scale and distributed solar systems</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="service-detail.php?service=energy-storage" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-battery-full"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Energy Storage</strong>
                            <span>Next-generation battery storage systems</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>
                </div>
            </div>

            <div class="services-menu-section">
                <h6 class="section-title">Additional Services</h6>
                <div class="services-menu-links">
                    <a href="service-detail.php?service=biomass-energy" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Biomass Energy</strong>
                            <span>Sustainable biomass power generation</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="service-detail.php?service=energy-consulting" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Energy Consulting</strong>
                            <span>Strategic energy planning and analysis</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="service-detail.php?service=om-services" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>O&M Services</strong>
                            <span>Operations and maintenance solutions</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="service-detail.php?service=hydroelectric-power" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-water"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Hydroelectric Power</strong>
                            <span>Clean water-based energy generation</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="service-detail.php?service=geothermal-energy" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Geothermal Energy</strong>
                            <span>Earth's natural heat for sustainable power</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="service-detail.php?service=smart-grid" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Smart Grid Solutions</strong>
                            <span>Intelligent energy distribution systems</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>
                </div>
            </div>

            <div class="services-menu-footer">
                <a href="services.php" class="btn btn-primary w-100">
                    <i class="fas fa-th-large me-2"></i>
                    View All Services
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Mobile App-Like Navigation JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle off-canvas menu navigation
            const offcanvasElement = document.getElementById('servicesOffcanvas');
            const offcanvas = new bootstrap.Offcanvas(offcanvasElement);

            // Close off-canvas when clicking on service links
            document.querySelectorAll('.service-menu-item').forEach(link => {
                link.addEventListener('click', function(e) {
                    // Close the off-canvas menu when navigating to service pages
                    offcanvas.hide();
                });
            });

            // Mobile menu enhancements
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');
            const mobileCloseBtn = document.querySelector('.mobile-close-btn');

            // Close mobile menu when clicking on nav links or close button
            document.querySelectorAll('.navbar-nav .nav-link, .mobile-close-btn').forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                            hide: true
                        });
                    }
                });
            });

            // Highlight current page in both desktop and mobile navigation
            const currentPage = window.location.pathname.split('/').pop() || 'index.php';
            
            // Desktop navigation highlighting
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                const href = link.getAttribute('href');
                if (href && (href === currentPage || href.includes(currentPage.replace('.php', '')))) {
                    link.classList.add('active');
                    link.style.color = '#22c55e';
                    link.style.fontWeight = '600';
                    link.style.position = 'relative';
                    // Add green underline indicator
                    link.style.borderBottom = '2px solid #22c55e';
                }
            });
            
            // Mobile footer highlighting
            document.querySelectorAll('.mobile-nav-item').forEach(item => {
                const href = item.getAttribute('href');
                if (href && href.includes(currentPage.replace('.php', ''))) {
                    item.classList.add('current-page');
                }
            });

            // Add haptic feedback simulation for mobile
            if ('vibrate' in navigator) {
                document.querySelectorAll('.mobile-nav-item, .nav-link, .mobile-close-btn').forEach(element => {
                    element.addEventListener('click', function() {
                        navigator.vibrate(50); // Short vibration for app-like feel
                    });
                });
            }

            // Add swipe gesture support for mobile menu
            let startY = 0;
            let startX = 0;

            navbarCollapse.addEventListener('touchstart', function(e) {
                startY = e.touches[0].clientY;
                startX = e.touches[0].clientX;
            });

            navbarCollapse.addEventListener('touchend', function(e) {
                const endY = e.changedTouches[0].clientY;
                const endX = e.changedTouches[0].clientX;
                const diffY = startY - endY;
                const diffX = Math.abs(startX - endX);

                // Swipe up to close menu
                if (diffY > 50 && diffX < 100) {
                    const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                        hide: true
                    });
                }
            });
        });
    </script>
