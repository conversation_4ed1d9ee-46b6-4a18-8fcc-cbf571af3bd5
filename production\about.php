<?php include 'header.php'; ?>

    <!-- Modern Hero Section -->
    <section id="modern-hero" class="modern-hero-section">
        <div class="hero-background-wrapper">
            <div class="hero-background-image">
                <img src="assets/images/demo-images/003 - Sequential number.webp" alt="Renewable Energy Future" class="hero-bg-img">
            </div>
            <div class="hero-gradient-overlay"></div>
            <div class="hero-particles"></div>
        </div>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10 text-center">
                    <div class="hero-content-modern">
                        <div class="hero-badge-modern">
                            <i class="fas fa-leaf"></i>
                            <span>About Eldergrove Energy</span>
                        </div>
                        <h1 class="hero-title-modern">
                            Pioneering the <span class="text-gradient-modern">Future</span><br>
                            of Renewable Energy
                        </h1>
                        <p class="hero-description-modern">
                            Leading the global transition to sustainable energy with innovative solutions
                            that power communities, protect our planet, and create lasting positive impact.
                        </p>
                        <div class="hero-actions-modern">
                            <a href="#company-intro" class="btn-modern-primary">
                                <span>Discover Our Story</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                            <a href="#our-services" class="btn-modern-outline">
                                <span>Our Solutions</span>
                            </a>
                        </div>
                        <div class="hero-scroll-indicator">
                            <div class="scroll-arrow">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Company Introduction Section -->
    <section id="company-intro" class="company-intro-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="intro-content">
                        <div class="section-badge-modern">
                            <i class="fas fa-building"></i>
                            <span>Who We Are</span>
                        </div>
                        <h2 class="section-title-modern">
                            Transforming Energy,<br>
                            <span class="text-accent">Empowering Tomorrow</span>
                        </h2>
                        <p class="intro-description">
                            Founded in 1971, Eldergrove Energy has evolved from a visionary startup to a global leader
                            in renewable energy solutions. We specialize in wind, solar, biomass, and energy storage
                            technologies that are reshaping how the world powers itself.
                        </p>
                        <div class="intro-actions">
                            <a href="contact-page.php" class="btn-about-primary">
                                <span>Get Started</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>

                        <style>
                        .intro-actions {
                            margin-top: 2rem;
                        }

                        .btn-about-primary {
                            display: inline-flex;
                            align-items: center;
                            gap: 0.75rem;
                            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
                            color: white;
                            padding: 1rem 2rem;
                            border-radius: 8px;
                            text-decoration: none;
                            font-weight: 600;
                            font-size: 1rem;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
                        }

                        .btn-about-primary:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
                            color: white;
                            text-decoration: none;
                        }

                        .company-intro-section {
                            padding: 6rem 0;
                            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                        }
                        </style>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="intro-visual">
                        <div class="visual-card">
                            <img src="https://images.unsplash.com/photo-1559302504-64aae6ca6b6d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Eldergrove Team" class="intro-image">
                            <div class="visual-overlay">
                                <div class="play-button">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                        </div>
                        <div class="floating-stats">
                            <div class="floating-stat">
                                <span class="stat-number">25GW+</span>
                                <span class="stat-label">Clean Energy</span>
                            </div>
                            <div class="floating-stat">
                                <span class="stat-number">20+</span>
                                <span class="stat-label">Years</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>    <!-- Our Story Section -->
    <section id="our-story" class="story-section">
        <div class="container">
            <div class="section-header-modern text-center">
                <div class="section-badge-modern">
                    <i class="fas fa-book"></i>
                    <span>Our Journey</span>
                </div>
                <h2 class="section-title-modern">
                    Five Decades of <span class="text-accent">Innovation</span>
                </h2>
                <p class="section-description-modern">
                    From a small startup to a global renewable energy leader, our journey has been
                    defined by innovation, sustainability, and an unwavering commitment to a cleaner future.
                </p>
            </div>

            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="story-card">
                        <div class="story-year">1971</div>
                        <h3 class="story-title">The Beginning</h3>
                        <p class="story-description">
                            Founded by visionary Alexander Webb Sr., Eldergrove Energy began as a renewable 
                            energy research company, pioneering early wind turbine designs and solar cell 
                            technology during the global energy crisis.
                        </p>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="story-card">
                        <div class="story-year">1980s</div>
                        <h3 class="story-title">Early Expansion</h3>
                        <p class="story-description">
                            Our first commercial wind farms were established across Europe, demonstrating 
                            the viability of large-scale renewable energy generation and setting industry 
                            standards for efficiency and reliability.
                        </p>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="story-card">
                        <div class="story-year">2000s</div>
                        <h3 class="story-title">Global Reach</h3>
                        <p class="story-description">
                            Expansion into offshore wind technology and international markets, with major 
                            projects in North America, Asia, and the Middle East. Introduction of our 
                            integrated energy storage solutions.
                        </p>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="story-card">
                        <div class="story-year">Today</div>
                        <h3 class="story-title">Leading Innovation</h3>
                        <p class="story-description">
                            Under CEO Alexander Webb Jr.'s leadership, we continue pushing boundaries with 
                            floating wind platforms, advanced energy storage, and smart grid integration 
                            across six continents.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>    <!-- Values & Mission Section -->
    <section id="values-mission" class="values-section">
        <div class="container">
            <div class="section-header-modern text-center">
                <div class="section-badge-modern">
                    <i class="fas fa-heart"></i>
                    <span>Our Values</span>
                </div>
                <h2 class="section-title-modern">
                    What <span class="text-accent">Drives Us</span>
                </h2>
                <p class="section-description-modern">
                    Our core values guide every decision we make and every project we undertake
                    in our mission to create a sustainable energy future.
                </p>
            </div>

            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <h3 class="service-title">Environmental Stewardship</h3>
                        <p class="service-description">
                            Protecting our planet through responsible renewable energy development that 
                            minimizes environmental impact while maximizing clean energy generation.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">Sustainability</span>
                            <span class="feature-tag">Conservation</span>
                            <span class="feature-tag">Green Future</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <h3 class="service-title">Innovation Excellence</h3>
                        <p class="service-description">
                            Continuously pushing the boundaries of renewable energy technology through 
                            research and development for more efficient and cost-effective solutions.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">R&D</span>
                            <span class="feature-tag">Technology</span>
                            <span class="feature-tag">Efficiency</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <h3 class="service-title">Integrity & Trust</h3>
                        <p class="service-description">
                            Building lasting relationships through transparent communication, ethical 
                            business practices, and reliable project delivery across all partnerships.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">Transparency</span>
                            <span class="feature-tag">Ethics</span>
                            <span class="feature-tag">Reliability</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="service-title">Community Impact</h3>
                        <p class="service-description">
                            Creating positive impacts in communities through local job creation, 
                            education support, and sustainable economic development initiatives.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">Local Jobs</span>
                            <span class="feature-tag">Education</span>
                            <span class="feature-tag">Development</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <h3 class="service-title">Global Responsibility</h3>
                        <p class="service-description">
                            Taking responsibility to combat climate change and accelerate the global 
                            transition to clean energy as a leader in renewable technology.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">Climate Action</span>
                            <span class="feature-tag">Global Impact</span>
                            <span class="feature-tag">Leadership</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h3 class="service-title">Future Focus</h3>
                        <p class="service-description">
                            Designing renewable energy solutions for the next generation, ensuring 
                            long-term sustainability and adaptability to evolving energy needs.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">Next-Gen</span>
                            <span class="feature-tag">Adaptability</span>
                            <span class="feature-tag">Longevity</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Key Statistics Section -->
    <section id="key-statistics" class="statistics-section">
        <div class="container">
            <div class="section-header-modern text-center">
                <div class="section-badge-modern">
                    <i class="fas fa-chart-bar"></i>
                    <span>Our Impact</span>
                </div>
                <h2 class="section-title-modern">
                    Measurable <span class="text-accent">Results</span>
                </h2>
                <p class="section-description-modern">
                    Two decades of dedication have resulted in significant achievements
                    that demonstrate our commitment to a sustainable future.
                </p>
            </div>

            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number-modern" data-target="25">0</div>
                            <div class="stat-unit">GW+</div>
                            <h4 class="stat-title-modern">Installed Capacity</h4>
                            <p class="stat-description-modern">Clean energy capacity deployed worldwide</p>
                        </div>
                        <div class="stat-progress">
                            <div class="progress-bar" data-width="85%"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern">
                            <i class="fas fa-globe-americas"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number-modern" data-target="30">0</div>
                            <div class="stat-unit">+</div>
                            <h4 class="stat-title-modern">Countries Served</h4>
                            <p class="stat-description-modern">Global presence across continents</p>
                        </div>
                        <div class="stat-progress">
                            <div class="progress-bar" data-width="75%"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number-modern" data-target="60">0</div>
                            <div class="stat-unit">M+</div>
                            <h4 class="stat-title-modern">Homes Powered</h4>
                            <p class="stat-description-modern">Families benefiting from clean energy</p>
                        </div>
                        <div class="stat-progress">
                            <div class="progress-bar" data-width="90%"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number-modern" data-target="95">0</div>
                            <div class="stat-unit">M</div>
                            <h4 class="stat-title-modern">Tons CO₂ Prevented</h4>
                            <p class="stat-description-modern">Annual carbon emissions avoided</p>
                        </div>
                        <div class="stat-progress">
                            <div class="progress-bar" data-width="95%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Global Impact Showcase Section -->
    <section id="global-impact" class="global-impact-section">
        <div class="impact-background">
            <div class="impact-bg-image">
                <img src="https://images.unsplash.com/photo-1569163139394-de4e4f43e4e3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" alt="Global Impact" class="bg-image">
            </div>
            <div class="impact-overlay"></div>
        </div>
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <div class="impact-content">
                        <div class="section-badge-modern light">
                            <i class="fas fa-earth-americas"></i>
                            <span>Global Impact</span>
                        </div>
                        <h2 class="section-title-modern light">
                            Powering a <span class="text-gradient-light">Sustainable Future</span>
                        </h2>
                        <p class="section-description-modern light">
                            Our commitment extends beyond energy generation. We're building a legacy of
                            environmental stewardship, economic growth, and social responsibility that
                            benefits communities worldwide.
                        </p>
                    </div>
                </div>
            </div>

            <div class="row impact-cards-row justify-content-center">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="impact-card-modern">
                        <div class="impact-card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="impact-card-title">Social</h3>
                        <p class="impact-card-description">
                            Creating 15,000+ jobs and empowering local communities through skills
                            development and sustainable economic opportunities.
                        </p>
                        <div class="impact-metrics">
                            <div class="metric">
                                <span class="metric-number">15K+</span>
                                <span class="metric-label">Jobs Created</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="impact-card-modern">
                        <div class="impact-card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="impact-card-title">Economic</h3>
                        <p class="impact-card-description">
                            Driving $50B+ in economic value through infrastructure investment,
                            energy cost savings, and sustainable development initiatives.
                        </p>
                        <div class="impact-metrics">
                            <div class="metric">
                                <span class="metric-number">$50B+</span>
                                <span class="metric-label">Economic Value</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="impact-card-modern">
                        <div class="impact-card-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <h3 class="impact-card-title">Environmental</h3>
                        <p class="impact-card-description">
                            Protecting ecosystems and promoting biodiversity through sustainable
                            energy practices and comprehensive environmental stewardship.
                        </p>
                        <div class="impact-metrics">
                            <div class="metric">
                                <span class="metric-number">500K+</span>
                                <span class="metric-label">Trees Equivalent</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </section>

    <?php include 'footer.php'; ?>

    <style>
    /* Story Section Styles */
    .story-section {
        padding: 6rem 0;
        background: #f8fafc;
    }

    .story-card {
        background: white;
        padding: 2.5rem 2rem;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        border: 1px solid #f1f5f9;
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .story-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .story-year {
        font-size: 2rem;
        font-weight: 800;
        color: #22c55e;
        margin-bottom: 1rem;
        position: relative;
    }

    .story-year::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
        border-radius: 2px;
    }

    .story-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 1rem;
    }

    .story-description {
        color: #64748b;
        line-height: 1.6;
        margin: 0;
    }    /* Values Section Styles */
    .values-section {
        padding: 6rem 0;
        background: white;
    }

    /* Use service card styles for values section */
    .values-section .service-card {
        background: white;
        border-radius: 20px;
        padding: 2.5rem 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        height: 100%;
        border: 1px solid #f1f5f9;
    }

    .values-section .service-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        border-color: #22c55e;
    }

    .values-section .service-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .values-section .service-card:hover .service-icon {
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
    }

    .values-section .service-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 1rem;
    }

    .values-section .service-description {
        color: #64748b;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .values-section .service-features {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .values-section .feature-tag {
        background: #f0fdf4;
        color: #16a34a;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
        border: 1px solid #bbf7d0;
    }
    </style>

    <script>
        // Modern About Page Animations and Interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit to avoid conflicts with main.js
            setTimeout(function() {
                // Animated Counter for Statistics
                const counters = document.querySelectorAll('.stat-number-modern');
                const observerOptions = {
                    threshold: 0.3,
                    rootMargin: '0px 0px -50px 0px'
                };

            const counterObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const counter = entry.target;
                        const target = parseInt(counter.getAttribute('data-target'));

                        // Ensure we have a valid target and add debugging
                        if (target && target > 0) {
                            console.log('Animating counter to:', target);
                            const duration = 2000;
                            const increment = target / (duration / 16);
                            let current = 0;

                            const updateCounter = () => {
                                if (current < target) {
                                    current += increment;
                                    counter.textContent = Math.ceil(current);
                                    requestAnimationFrame(updateCounter);
                                } else {
                                    counter.textContent = target;
                                }
                            };

                            // Small delay before starting animation
                            setTimeout(updateCounter, 300);
                            counterObserver.unobserve(counter);
                        } else {
                            console.log('Invalid target for counter:', counter, 'data-target:', counter.getAttribute('data-target'));
                        }
                    }
                });
            }, observerOptions);

            counters.forEach(counter => {
                counterObserver.observe(counter);
            });

            // Progress Bar Animation
            const progressBars = document.querySelectorAll('.progress-bar');
            const progressObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const progressBar = entry.target;
                        const width = progressBar.getAttribute('data-width');
                        setTimeout(() => {
                            progressBar.style.width = width;
                        }, 500);
                        progressObserver.unobserve(progressBar);
                    }
                });
            }, observerOptions);

            progressBars.forEach(bar => {
                progressObserver.observe(bar);
            });

            // Smooth Scroll for Hero Actions (About page specific)
            document.querySelectorAll('.hero-actions-modern a[href^="#"], .cta-actions a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const headerHeight = document.querySelector('.header-nav')?.offsetHeight || 80;
                        const targetPosition = target.offsetTop - headerHeight;
                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Hero Scroll Indicator Animation
            const scrollIndicator = document.querySelector('.hero-scroll-indicator');
            if (scrollIndicator) {
                setInterval(() => {
                    scrollIndicator.style.transform = 'translateY(10px)';
                    setTimeout(() => {
                        scrollIndicator.style.transform = 'translateY(0)';
                    }, 500);
                }, 2000);
            }

            console.log('Modern About page loaded successfully with enhanced interactions!');
            }, 500); // Delay to avoid conflicts with main.js
        });
    </script>
