# Eldergrove Energy - Production .htaccess Configuration
# Security-hardened configuration for production deployment

# ============================================================================
# SECURITY HEADERS
# ============================================================================

<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Prevent clickjacking
    Header always set X-Frame-Options "DENY"
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Permissions policy
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()"
    
    # Content Security Policy (adjust as needed)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://www.google.com https://www.gstatic.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https: http:; connect-src 'self'; frame-src 'self' https://www.google.com; object-src 'none'; base-uri 'self';"
    
    # Remove server signature
    Header always unset Server
    Header always unset X-Powered-By
</IfModule>

# ============================================================================
# CLEAN URLs - REMOVE .php EXTENSIONS
# ============================================================================

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Remove .php extension from URLs
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^([^\.]+)$ $1.php [NC,L]
    
    # Redirect .php URLs to clean URLs
    RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
    RewriteRule ^ /%1? [NC,L,R=301]
    
    # Handle specific routes
    RewriteRule ^contact$ contact-page.php [NC,L]
    RewriteRule ^service/([^/]+)/?$ service-detail.php?service=$1 [NC,L,QSA]
    
    # Force HTTPS (uncomment for production with SSL)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Remove trailing slashes
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [R=301,L]
</IfModule>

# ============================================================================
# FILE SECURITY & ACCESS CONTROL
# ============================================================================

# Deny access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf|bak|backup|swp|tmp)$">
    Require all denied
</FilesMatch>

# Deny access to specific files
<Files "contact_log.txt">
    Require all denied
</Files>

<Files "composer.json">
    Require all denied
</Files>

<Files "composer.lock">
    Require all denied
</Files>

<Files "package.json">
    Require all denied
</Files>

<Files "package-lock.json">
    Require all denied
</Files>

# Deny access to directories
<DirectoryMatch "^.*/\.(git|svn|hg|bzr)/">
    Require all denied
</DirectoryMatch>

<DirectoryMatch "^.*/node_modules/">
    Require all denied
</DirectoryMatch>

<DirectoryMatch "^.*/vendor/">
    Require all denied
</DirectoryMatch>

<DirectoryMatch "^.*/tests?/">
    Require all denied
</DirectoryMatch>

# ============================================================================
# PERFORMANCE OPTIMIZATION
# ============================================================================

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Cache control headers
<IfModule mod_headers.c>
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|webp|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
    
    <FilesMatch "\.(html|php)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
</IfModule>

# ============================================================================
# ERROR HANDLING
# ============================================================================

# Custom error pages (create these files)
ErrorDocument 400 /error/400.html
ErrorDocument 401 /error/401.html
ErrorDocument 403 /error/403.html
ErrorDocument 404 /error/404.html
ErrorDocument 500 /error/500.html

# Disable server signature
ServerSignature Off

# ============================================================================
# ADDITIONAL SECURITY MEASURES
# ============================================================================

# Disable directory browsing
Options -Indexes

# Disable server-side includes
Options -Includes

# Disable CGI execution
Options -ExecCGI

# Prevent access to PHP configuration
<Files "php.ini">
    Require all denied
</Files>

# Block suspicious request methods
<LimitExcept GET POST HEAD>
    Require all denied
</LimitExcept>

# Block suspicious user agents
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (libwww-perl|python|nikto|scan|java|winhttp|clshttp|loader) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (%0A|%0D|%27|%3C|%3E|%00) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (;|<|>|'|"|\)|\(|%0A|%0D|%22|%27|%28|%3C|%3E|%00).*(libwww-perl|python|nikto|scan|java|winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Rate limiting (basic)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
