# Eldergrove Energy - Clean URLs Configuration
# Minimal configuration for clean URL functionality

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle specific routes first (before general rules)
    RewriteRule ^contact$ contact-page.php [NC,L]
    RewriteRule ^service/([^/]+)/?$ service-detail.php?service=$1 [NC,L,QSA]

    # Redirect .php URLs to clean URLs (except contact.php for form processing)
    RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
    RewriteCond %{REQUEST_URI} !^/contact\.php$ [NC]
    RewriteRule ^ /%1? [NC,L,R=301]

    # Remove .php extension from URLs (general rule)
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^([^\.]+)$ $1.php [NC,L]
    
    # Remove trailing slashes
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [R=301,L]
</IfModule>
