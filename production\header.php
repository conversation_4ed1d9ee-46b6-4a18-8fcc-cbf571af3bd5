<?php
// Security Headers for Production
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('Permissions-Policy: geolocation=(), microphone=(), camera=()');

// CSRF Token Generation
session_start();
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Dynamic Base URL Function
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script = $_SERVER['SCRIPT_NAME'];
    $path = dirname($script);

    // Clean up the path
    $path = rtrim($path, '/');
    if ($path === '' || $path === '.') {
        $path = '';
    }

    return $protocol . '://' . $host . $path;
}

// Get the base URL for this installation
$baseUrl = getBaseUrl();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eldergrove - Leading the Global Renewable Energy Revolution</title>

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍃</text></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍃</text></svg>">

    <!-- SEO Meta Tags -->
    <meta name="description" content="Eldergrove Energy - Leading global provider of renewable energy solutions including wind, solar, biomass, and energy storage. Powering a sustainable future since 1971.">
    <meta name="keywords" content="renewable energy, wind power, solar energy, biomass, energy storage, offshore wind, onshore wind, sustainable energy, clean energy, green technology">
    <meta name="author" content="Eldergrove Energy">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">
    <meta name="revisit-after" content="7 days">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Eldergrove Energy - Renewable Energy Solutions">
    <meta property="og:description" content="Leading global provider of renewable energy solutions including wind, solar, biomass, and energy storage. Powering a sustainable future since 1971.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://eldergroveenergy.com">
    <meta property="og:image" content="https://eldergroveenergy.com/assets/images/logos/logo.png">
    <meta property="og:site_name" content="Eldergrove Energy">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Eldergrove Energy - Renewable Energy Solutions">
    <meta name="twitter:description" content="Leading global provider of renewable energy solutions including wind, solar, biomass, and energy storage.">
    <meta name="twitter:image" content="https://eldergroveenergy.com/assets/images/logos/logo.png">

    <!-- Performance Optimization -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//images.unsplash.com">
    <link rel="dns-prefetch" href="//translate.google.com">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/slider.css">

    <!-- Logo Styles -->
    <style>
        .brand-logo-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .brand-logo-img {
            height: 50px;
            width: auto;
            transition: all 0.3s ease;
        }

        /* Mobile Navigation Improvements */
        .mobile-menu-header {
            display: flex;
            justify-content: flex-end;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .mobile-close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #64748b;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .mobile-close-btn:hover {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        /* Mobile logo sizing */
        @media (max-width: 768px) {
            .brand-logo-img {
                height: 58px;
            }

            /* Mobile menu styling */
            .navbar-collapse {
                background: white;
                border-radius: 0 0 15px 15px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                margin-top: 1rem;
            }

            /* Mobile menu dividers */
            .navbar-nav .nav-item {
                border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            }

            .navbar-nav .nav-item:last-child {
                border-bottom: none;
            }

            .navbar-nav .nav-link {
                padding: 1rem 1.5rem;
                color: #1e293b;
                font-weight: 500;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .navbar-nav .nav-link:hover {
                background: rgba(34, 197, 94, 0.1);
                color: #22c55e;
                padding-left: 2rem;
            }



            /* Mobile app-like animations */
            .navbar-nav .nav-link {
                position: relative;
                overflow: hidden;
            }

            /* Smooth hamburger animation */
            .navbar-toggler {
                border: none;
                padding: 0.5rem;
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .navbar-toggler:focus {
                box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
            }

            .navbar-toggler-icon {
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2834, 197, 94, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
            }

            /* Mobile menu slide animation */
            .navbar-collapse.collapsing,
            .navbar-collapse.show {
                animation: slideDown 0.3s ease-out;
            }

            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        }

        .navbar-brand:hover .brand-logo-img {
            transform: scale(1.1);
        }

        .brand-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            letter-spacing: -0.025em;
        }

        .navbar-brand:hover .brand-text {
            color: #22c55e;
        }

        @media (max-width: 768px) {
            .brand-logo-img {
                height: 32px;
            }

            .brand-text {
                font-size: 1.25rem;
            }
        }



        /* Navigation Layout Fixes */
        .navbar-nav {
            align-items: center;
        }

        .nav-item {
            display: flex;
            align-items: center;
        }

        /* Custom Language Switcher Styles */
        .language-switcher {
            position: relative;
            margin-left: 15px;
        }

        .language-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 10px 15px;
            font-size: 14px;
            color: #1e293b;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            min-width: 100px;
            height: 42px;
        }

        .language-btn:hover {
            border-color: #22c55e;
            background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.15);
            transform: translateY(-1px);
        }

        .language-btn .fa-globe {
            color: #22c55e;
            font-size: 16px;
        }

        .current-lang {
            font-weight: 600;
            color: #1e293b;
        }

        .language-btn .fa-chevron-down {
            font-size: 12px;
            color: #64748b;
            transition: transform 0.3s ease;
        }

        .language-btn.active .fa-chevron-down {
            transform: rotate(180deg);
        }

        .language-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: #ffffff;
            border: 2px solid #22c55e;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .language-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .language-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f1f5f9;
            font-size: 14px;
        }

        .language-option:last-child {
            border-bottom: none;
        }

        .language-option:hover {
            background: #f0fdf4;
            color: #22c55e;
        }

        .language-option.active {
            background: #22c55e;
            color: white;
        }

        .flag-icon {
            font-size: 18px;
            width: 24px;
            text-align: center;
        }

        .lang-name {
            font-weight: 500;
            flex: 1;
        }

        /* Mobile Language Switcher */
        @media (max-width: 768px) {
            .language-switcher {
                margin: 15px 20px;
                width: calc(100% - 40px);
            }

            .language-btn {
                width: 100%;
                justify-content: space-between;
                min-width: auto;
                padding: 15px 20px;
                font-size: 16px;
                height: 50px;
                border-radius: 15px;
            }

            .language-btn .fa-globe {
                font-size: 18px;
            }

            .current-lang {
                font-size: 16px;
                font-weight: 600;
            }

            .language-dropdown {
                right: 0;
                left: 0;
                min-width: auto;
                border-radius: 15px;
                margin-top: 5px;
                max-height: 300px;
                overflow-y: auto;
            }

            .language-option {
                padding: 15px 20px;
                font-size: 16px;
                border-bottom: 1px solid #f1f5f9;
            }

            .language-option:last-child {
                border-bottom: none;
                border-radius: 0 0 15px 15px;
            }

            .language-option:first-child {
                border-radius: 15px 15px 0 0;
            }

            .flag-icon {
                font-size: 20px;
                width: 28px;
            }

            .lang-name {
                font-size: 16px;
                font-weight: 500;
            }

            /* Mobile menu integration */
            .navbar-nav .nav-item:has(.language-switcher) {
                border-bottom: none;
                padding: 0;
            }

            /* Mobile-specific animations */
            .language-dropdown.show {
                animation: mobileSlideDown 0.3s ease-out;
            }

            @keyframes mobileSlideDown {
                from {
                    opacity: 0;
                    transform: translateY(-20px) scale(0.95);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
        }

        /* Performance Optimizations */
        img {
            loading: lazy;
        }

        /* Critical CSS for above-the-fold content */
        .navbar {
            will-change: transform;
        }

        .hero-slide-bg-img,
        .hero-bg-img {
            object-fit: cover;
            object-position: center;
        }

        /* Reduce layout shifts */
        .container {
            max-width: 1200px;
        }

        /* Optimize animations */
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
    </style>



    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Eldergrove Energy",
        "url": "https://eldergroveenergy.com",
        "logo": "https://eldergroveenergy.com/assets/images/logos/logo.png",
        "description": "Leading global provider of renewable energy solutions including wind, solar, biomass, and energy storage.",
        "foundingDate": "1971",
        "industry": "Renewable Energy",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "25 Canada Square, Canary Wharf",
            "addressLocality": "London",
            "postalCode": "E14 5LQ",
            "addressCountry": "UK"
        },
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+44-20-7946-0958",
            "contactType": "customer service",
            "email": "<EMAIL>"
        },

        "services": [
            "Wind Energy Solutions",
            "Solar Power Systems",
            "Energy Storage",
            "Biomass Energy",
            "Smart Grid Technology",
            "Energy Consulting"
        ]
    }
    </script>

    <!-- Advanced Translation System -->
    <script type="text/javascript">
        // Advanced translation system with multiple fallback methods
        let currentLanguage = 'en';
        let isTranslating = false;
        let translationMethod = 'client'; // 'google', 'client', 'hybrid'
        let translations = {};

        // Translation dictionaries for key terms
        const translationDictionary = {
            'es': {
                'About': 'Acerca de',
                'Home': 'Inicio',
                'Services': 'Servicios',
                'Contact': 'Contacto',
                'Team': 'Equipo',
                'Projects': 'Proyectos',
                'Technology': 'Tecnología',
                'Sustainability': 'Sostenibilidad',
                'Renewable Energy': 'Energía Renovable',
                'Wind Energy': 'Energía Eólica',
                'Solar Power': 'Energía Solar',
                'Energy Storage': 'Almacenamiento de Energía',
                'Biomass Energy': 'Energía de Biomasa',
                'Leading the global transition': 'Liderando la transición global',
                'Pioneering the Future': 'Pioneros del Futuro',
                'Clean Energy': 'Energía Limpia',
                'Get Started': 'Comenzar',
                'Our Solutions': 'Nuestras Soluciones',
                'Who We Are': 'Quiénes Somos',
                'Our Journey': 'Nuestro Viaje',
                'Our Values': 'Nuestros Valores',
                'What Drives Us': 'Lo que nos Impulsa',
                'Environmental Stewardship': 'Gestión Ambiental',
                'Innovation Excellence': 'Excelencia en Innovación',
                'Global Impact': 'Impacto Global',
                'Powering a Sustainable Future': 'Impulsando un Futuro Sostenible'
            },
            'fr': {
                'About': 'À propos',
                'Home': 'Accueil',
                'Services': 'Services',
                'Contact': 'Contact',
                'Team': 'Équipe',
                'Projects': 'Projets',
                'Technology': 'Technologie',
                'Sustainability': 'Durabilité',
                'Renewable Energy': 'Énergie Renouvelable',
                'Wind Energy': 'Énergie Éolienne',
                'Solar Power': 'Énergie Solaire',
                'Energy Storage': 'Stockage d\'Énergie',
                'Biomass Energy': 'Énergie de Biomasse',
                'Leading the global transition': 'Menant la transition mondiale',
                'Pioneering the Future': 'Pionnier de l\'Avenir',
                'Clean Energy': 'Énergie Propre',
                'Get Started': 'Commencer',
                'Our Solutions': 'Nos Solutions',
                'Who We Are': 'Qui Nous Sommes',
                'Our Journey': 'Notre Parcours',
                'Our Values': 'Nos Valeurs',
                'What Drives Us': 'Ce qui nous Motive',
                'Environmental Stewardship': 'Gestion Environnementale',
                'Innovation Excellence': 'Excellence en Innovation',
                'Global Impact': 'Impact Mondial',
                'Powering a Sustainable Future': 'Alimenter un Avenir Durable'
            },
            'de': {
                'About': 'Über uns',
                'Home': 'Startseite',
                'Services': 'Dienstleistungen',
                'Contact': 'Kontakt',
                'Team': 'Team',
                'Projects': 'Projekte',
                'Technology': 'Technologie',
                'Sustainability': 'Nachhaltigkeit',
                'Renewable Energy': 'Erneuerbare Energie',
                'Wind Energy': 'Windenergie',
                'Solar Power': 'Solarenergie',
                'Energy Storage': 'Energiespeicherung',
                'Biomass Energy': 'Biomasseenergie',
                'Leading the global transition': 'Den globalen Wandel anführen',
                'Pioneering the Future': 'Pionier der Zukunft',
                'Clean Energy': 'Saubere Energie',
                'Get Started': 'Loslegen',
                'Our Solutions': 'Unsere Lösungen',
                'Who We Are': 'Wer wir sind',
                'Our Journey': 'Unsere Reise',
                'Our Values': 'Unsere Werte',
                'What Drives Us': 'Was uns antreibt',
                'Environmental Stewardship': 'Umweltverantwortung',
                'Innovation Excellence': 'Innovationsexzellenz',
                'Global Impact': 'Globale Auswirkungen',
                'Powering a Sustainable Future': 'Eine nachhaltige Zukunft antreiben'
            }
        };

        // Initialize translation system
        function initializeTranslationSystem() {
            console.log('Initializing advanced translation system...');

            // Try Google Translate first, fallback to client-side
            if (typeof google !== 'undefined' && google.translate) {
                try {
                    initializeGoogleTranslate();
                } catch (error) {
                    console.warn('Google Translate failed, using client-side translation:', error);
                    translationMethod = 'client';
                }
            } else {
                console.log('Using client-side translation system');
                translationMethod = 'client';
            }
        }

        function initializeGoogleTranslate() {
            try {
                new google.translate.TranslateElement({
                    pageLanguage: 'en',
                    includedLanguages: 'en,es,fr,de,zh,ja,ar,pt,ru,hi',
                    layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
                    autoDisplay: false,
                    multilanguagePage: true
                }, 'hidden-google-translate');

                console.log('Google Translate initialized successfully');
                translationMethod = 'google';

                // Hide all Google Translate UI elements
                setTimeout(hideGoogleTranslateElements, 500);

                // Check if combo is available
                setTimeout(() => {
                    const combo = document.querySelector('.goog-te-combo');
                    if (!combo || combo.options.length <= 1) {
                        console.warn('Google Translate combo not available, switching to client-side');
                        translationMethod = 'client';
                    }
                }, 2000);

            } catch (error) {
                console.error('Google Translate initialization failed:', error);
                translationMethod = 'client';
            }
        }

        function hideGoogleTranslateElements() {
            // Hide all Google Translate banners and UI
            const elementsToHide = [
                '.goog-te-banner-frame',
                '.goog-te-ftab',
                '.goog-te-balloon-frame',
                '.goog-te-gadget'
            ];

            elementsToHide.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                });
            });

            // Reset body position
            document.body.style.top = '0';
            document.body.style.position = 'static';
        }

        // Client-side translation function
        function translateWithClientSide(targetLang) {
            console.log('Using client-side translation for:', targetLang);

            if (!translationDictionary[targetLang]) {
                console.warn('Translation not available for language:', targetLang);
                showTranslationError('Translation not available for this language');
                return;
            }

            const translations = translationDictionary[targetLang];
            let translatedCount = 0;

            // Translate navigation elements
            const navElements = document.querySelectorAll('nav a, .navbar a, header a');
            navElements.forEach(element => {
                const text = element.textContent.trim();
                if (translations[text]) {
                    element.textContent = translations[text];
                    translatedCount++;
                }
            });

            // Translate headings
            const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
            headings.forEach(element => {
                const text = element.textContent.trim();
                if (translations[text]) {
                    element.textContent = translations[text];
                    translatedCount++;
                } else {
                    // Try partial matches for longer headings
                    Object.keys(translations).forEach(key => {
                        if (text.includes(key)) {
                            element.textContent = element.textContent.replace(key, translations[key]);
                            translatedCount++;
                        }
                    });
                }
            });

            // Translate buttons and links
            const buttons = document.querySelectorAll('button, .btn, a.button');
            buttons.forEach(element => {
                const text = element.textContent.trim();
                if (translations[text]) {
                    element.textContent = translations[text];
                    translatedCount++;
                }
            });

            // Translate specific content areas
            const contentElements = document.querySelectorAll('p, span, div');
            contentElements.forEach(element => {
                if (element.children.length === 0) { // Only text nodes
                    const text = element.textContent.trim();
                    if (translations[text]) {
                        element.textContent = translations[text];
                        translatedCount++;
                    } else {
                        // Try partial matches
                        Object.keys(translations).forEach(key => {
                            if (text.includes(key)) {
                                element.textContent = element.textContent.replace(key, translations[key]);
                                translatedCount++;
                            }
                        });
                    }
                }
            });

            console.log(`Client-side translation completed. Translated ${translatedCount} elements.`);

            // Update UI to show completion
            setTimeout(() => {
                updateLanguageButton(targetLang);
                showTranslationSuccess(`Page translated to ${getLanguageName(targetLang)}`);
            }, 500);
        }

        function getLanguageName(code) {
            const languageNames = {
                'en': 'English',
                'es': 'Spanish',
                'fr': 'French',
                'de': 'German',
                'zh': 'Chinese',
                'ja': 'Japanese',
                'ar': 'Arabic',
                'pt': 'Portuguese',
                'ru': 'Russian',
                'hi': 'Hindi'
            };
            return languageNames[code] || code.toUpperCase();
        }

        function showTranslationSuccess(message) {
            console.log('Translation success:', message);
            // You can add a toast notification here if desired
        }

        function showTranslationError(message) {
            console.error('Translation error:', message);
            resetTranslationUI();
            // You can add error notification here if desired
        }

        // Language switcher functionality
        document.addEventListener('DOMContentLoaded', function() {
            const languageBtn = document.getElementById('languageBtn');
            const languageDropdown = document.getElementById('languageDropdown');
            const languageOptions = document.querySelectorAll('.language-option');

            // Initialize translation system
            initializeTranslationSystem();

            // Toggle dropdown
            languageBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                languageDropdown.classList.toggle('show');
                languageBtn.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                languageDropdown.classList.remove('show');
                languageBtn.classList.remove('active');
            });

            // Mobile-specific enhancements
            if (window.innerWidth <= 768) {
                // Add haptic feedback for mobile
                if ('vibrate' in navigator) {
                    languageBtn.addEventListener('click', function() {
                        navigator.vibrate(30);
                    });

                    languageOptions.forEach(option => {
                        option.addEventListener('click', function() {
                            navigator.vibrate(50);
                        });
                    });
                }

                // Close mobile menu when language is selected
                languageOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        setTimeout(() => {
                            const navbarCollapse = document.querySelector('.navbar-collapse');
                            if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                                const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                                    hide: true
                                });
                            }
                        }, 1000); // Wait for translation to start
                    });
                });
            }

            // Handle language selection
            languageOptions.forEach(option => {
                option.addEventListener('click', function(e) {
                    e.stopPropagation();

                    if (isTranslating) return;

                    const selectedLang = this.dataset.lang;
                    const selectedName = this.dataset.name;

                    if (selectedLang === currentLanguage) {
                        languageDropdown.classList.remove('show');
                        languageBtn.classList.remove('active');
                        return;
                    }

                    // Update UI
                    languageOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');

                    // Update button text
                    const currentLangSpan = document.querySelector('.current-lang');
                    currentLangSpan.textContent = selectedLang.toUpperCase();

                    // Close dropdown
                    languageDropdown.classList.remove('show');
                    languageBtn.classList.remove('active');

                    // Translate page
                    translatePage(selectedLang);
                    currentLanguage = selectedLang;
                });
            });

            // Initialize hidden Google Translate element
            const hiddenDiv = document.createElement('div');
            hiddenDiv.id = 'hidden-google-translate';
            hiddenDiv.style.display = 'none';
            document.body.appendChild(hiddenDiv);

            // Monitor for Google Translate elements and hide them
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) {
                            hideGoogleTranslateElements();
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });

        // Enhanced translation function with multiple methods
        function translatePage(targetLang) {
            if (isTranslating) return;

            isTranslating = true;
            console.log(`Starting translation to ${targetLang} using method: ${translationMethod}`);

            // Show loading state
            const languageBtn = document.getElementById('languageBtn');
            languageBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Translating...</span>';
            languageBtn.disabled = true;

            // Choose translation method
            if (translationMethod === 'google') {
                translateWithGoogle(targetLang);
            } else {
                // Use client-side translation
                setTimeout(() => {
                    translateWithClientSide(targetLang);
                    isTranslating = false;
                }, 500); // Small delay for better UX
            }
        }

        function translateWithGoogle(targetLang) {
            // Wait for Google Translate to be ready
            function waitForGoogleTranslate(callback, maxAttempts = 10) {
                let attempts = 0;

                function checkForCombo() {
                    attempts++;
                    const googleTranslateCombo = document.querySelector('.goog-te-combo');

                    if (googleTranslateCombo && googleTranslateCombo.options.length > 1) {
                        callback(googleTranslateCombo);
                    } else if (attempts < maxAttempts) {
                        setTimeout(checkForCombo, 250);
                    } else {
                        console.warn('Google Translate not ready, switching to client-side translation');
                        translationMethod = 'client';
                        translateWithClientSide(targetLang);
                        isTranslating = false;
                    }
                }

                checkForCombo();
            }

            function resetTranslationUI() {
                try {
                    const currentLangSpan = document.querySelector('.current-lang');
                    const languageBtn = document.getElementById('languageBtn');
                    
                    if (currentLangSpan) {
                        currentLangSpan.textContent = targetLang.toUpperCase();
                    }
                    
                    if (languageBtn) {
                        languageBtn.innerHTML = '<i class="fas fa-globe"></i> <span class="current-lang">' + targetLang.toUpperCase() + '</span> <i class="fas fa-chevron-down"></i>';
                        languageBtn.disabled = false;
                    }
                    
                    isTranslating = false;
                    hideGoogleTranslateElements();
                } catch (e) {
                    console.error('Error in resetTranslationUI:', e);
                    isTranslating = false;
                }
            }

            try {
                waitForGoogleTranslate(function(googleTranslateCombo) {
                    if (targetLang === 'en') {
                        // Reset to original language
                        googleTranslateCombo.value = '';
                        googleTranslateCombo.dispatchEvent(new Event('change'));
                    } else {
                        // Translate to target language
                        googleTranslateCombo.value = targetLang;
                        googleTranslateCombo.dispatchEvent(new Event('change'));
                    }

                    // Reset UI after translation
                    setTimeout(() => {
                        resetTranslationUI();
                    }, 1500);
                });

            } catch (error) {
                console.error('Translation error:', error);
                resetTranslationUI();
            }
        }

        // Load Google Translate script with fallback
        function loadGoogleTranslate() {
            // Check if script is already loaded
            if (document.querySelector('script[src*="translate.google.com"]')) {
                console.log('Google Translate script already loaded');
                return;
            }

            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = 'https://translate.google.com/translate_a/element.js?cb=initializeTranslationSystem';
            script.onerror = function() {
                console.error('Failed to load Google Translate script');
                showTranslationError('Translation service currently unavailable');
            };
            script.onload = function() {
                console.log('Google Translate script loaded successfully');
            };
            
            // Set timeout to detect if script fails to initialize
            setTimeout(() => {
                if (!googleTranslateAvailable) {
                    showTranslationError('Translation service timed out');
                }
            }, 5000);

            document.head.appendChild(script);
        }

        // Show translation error message
        function showTranslationError(message) {
            const errorElement = document.createElement('div');
            errorElement.className = 'translation-error';
            errorElement.style.position = 'fixed';
            errorElement.style.bottom = '20px';
            errorElement.style.right = '20px';
            errorElement.style.backgroundColor = '#ffebee';
            errorElement.style.color = '#c62828';
            errorElement.style.padding = '10px 15px';
            errorElement.style.borderRadius = '4px';
            errorElement.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
            errorElement.style.zIndex = '9999';
            errorElement.textContent = message;
            
            document.body.appendChild(errorElement);
            
            // Remove after 5 seconds
            setTimeout(() => {
                errorElement.remove();
            }, 5000);
        }

        // Initialize when page loads
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadGoogleTranslate);
        } else {
            loadGoogleTranslate();
        }
    </script>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header-nav fixed-top">
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <!-- Brand Logo -->
                <a class="navbar-brand" href="<?php echo $baseUrl; ?>/">
                    <img src="assets/images/logos/logo.png" alt="Eldergrove Energy" class="brand-logo-img">
                </a>

                <!-- Mobile Toggle Button -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Navigation Menu -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <!-- Mobile Close Button -->
                    <div class="mobile-menu-header d-lg-none">
                        <button type="button" class="mobile-close-btn" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $baseUrl; ?>/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $baseUrl; ?>/about">About</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $baseUrl; ?>/team">Team</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link offcanvas-trigger" href="#" data-bs-toggle="offcanvas" data-bs-target="#servicesOffcanvas">
                                Services
                                <i class="fas fa-chevron-right ms-1"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $baseUrl; ?>/projects">Projects</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $baseUrl; ?>/technology">Technology</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $baseUrl; ?>/sustainability">Sustainability</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $baseUrl; ?>/contact">Contact</a>
                        </li>
                        <li class="nav-item">
                            <div class="language-switcher">
                                <button class="language-btn" id="languageBtn" aria-label="Select Language">
                                    <i class="fas fa-globe"></i>
                                    <span class="current-lang">EN</span>
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <div class="language-dropdown" id="languageDropdown">
                                    <div class="language-option active" data-lang="en" data-name="English">
                                        <span class="flag-icon">🇺🇸</span>
                                        <span class="lang-name">English</span>
                                    </div>
                                    <div class="language-option" data-lang="es" data-name="Español">
                                        <span class="flag-icon">🇪🇸</span>
                                        <span class="lang-name">Español</span>
                                    </div>
                                    <div class="language-option" data-lang="fr" data-name="Français">
                                        <span class="flag-icon">🇫🇷</span>
                                        <span class="lang-name">Français</span>
                                    </div>
                                    <div class="language-option" data-lang="de" data-name="Deutsch">
                                        <span class="flag-icon">🇩🇪</span>
                                        <span class="lang-name">Deutsch</span>
                                    </div>
                                    <div class="language-option" data-lang="zh" data-name="中文">
                                        <span class="flag-icon">🇨🇳</span>
                                        <span class="lang-name">中文</span>
                                    </div>
                                    <div class="language-option" data-lang="ja" data-name="日本語">
                                        <span class="flag-icon">🇯🇵</span>
                                        <span class="lang-name">日本語</span>
                                    </div>
                                    <div class="language-option" data-lang="ar" data-name="العربية">
                                        <span class="flag-icon">🇸🇦</span>
                                        <span class="lang-name">العربية</span>
                                    </div>
                                    <div class="language-option" data-lang="pt" data-name="Português">
                                        <span class="flag-icon">🇵🇹</span>
                                        <span class="lang-name">Português</span>
                                    </div>
                                    <div class="language-option" data-lang="ru" data-name="Русский">
                                        <span class="flag-icon">🇷🇺</span>
                                        <span class="lang-name">Русский</span>
                                    </div>
                                    <div class="language-option" data-lang="hi" data-name="हिन्दी">
                                        <span class="flag-icon">🇮🇳</span>
                                        <span class="lang-name">हिन्दी</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Off-canvas Services Menu -->
    <div class="offcanvas offcanvas-end services-offcanvas" tabindex="-1" id="servicesOffcanvas" aria-labelledby="servicesOffcanvasLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="servicesOffcanvasLabel">
                <i class="fas fa-cogs me-2"></i>
                Our Services
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <div class="services-menu-header">
                <h6>Renewable Energy Solutions</h6>
                <p>Comprehensive energy technologies for a sustainable future</p>
            </div>

            <div class="services-menu-section">
                <h6 class="section-title">Primary Technologies</h6>
                <div class="services-menu-links">
                    <a href="<?php echo $baseUrl; ?>/service/wind-energy" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-wind"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Wind Energy</strong>
                            <span>Advanced onshore and offshore wind solutions</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="<?php echo $baseUrl; ?>/service/solar-power" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-solar-panel"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Solar Power</strong>
                            <span>Utility-scale and distributed solar systems</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="<?php echo $baseUrl; ?>/service/energy-storage" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-battery-full"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Energy Storage</strong>
                            <span>Next-generation battery storage systems</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>
                </div>
            </div>

            <div class="services-menu-section">
                <h6 class="section-title">Additional Services</h6>
                <div class="services-menu-links">
                    <a href="<?php echo $baseUrl; ?>/service/biomass-energy" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Biomass Energy</strong>
                            <span>Sustainable biomass power generation</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="<?php echo $baseUrl; ?>/service/energy-consulting" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Energy Consulting</strong>
                            <span>Strategic energy planning and analysis</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="<?php echo $baseUrl; ?>/service/om-services" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>O&M Services</strong>
                            <span>Operations and maintenance solutions</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="<?php echo $baseUrl; ?>/service/hydroelectric-power" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-water"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Hydroelectric Power</strong>
                            <span>Clean water-based energy generation</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="<?php echo $baseUrl; ?>/service/geothermal-energy" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Geothermal Energy</strong>
                            <span>Earth's natural heat for sustainable power</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>

                    <a href="<?php echo $baseUrl; ?>/service/smart-grid" class="service-menu-item">
                        <div class="service-menu-icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <div class="service-menu-content">
                            <strong>Smart Grid Solutions</strong>
                            <span>Intelligent energy distribution systems</span>
                        </div>
                        <i class="fas fa-chevron-right service-menu-arrow"></i>
                    </a>
                </div>
            </div>

            <div class="services-menu-footer">
                <a href="<?php echo $baseUrl; ?>/services" class="btn btn-primary w-100">
                    <i class="fas fa-th-large me-2"></i>
                    View All Services
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Mobile App-Like Navigation JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle off-canvas menu navigation
            const offcanvasElement = document.getElementById('servicesOffcanvas');
            const offcanvas = new bootstrap.Offcanvas(offcanvasElement);

            // Close off-canvas when clicking on service links
            document.querySelectorAll('.service-menu-item').forEach(link => {
                link.addEventListener('click', function(e) {
                    // Close the off-canvas menu when navigating to service pages
                    offcanvas.hide();
                });
            });

            // Mobile menu enhancements
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');
            const mobileCloseBtn = document.querySelector('.mobile-close-btn');

            // Close mobile menu when clicking on nav links or close button
            document.querySelectorAll('.navbar-nav .nav-link, .mobile-close-btn').forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                            hide: true
                        });
                    }
                });
            });

            // Highlight current page in both desktop and mobile navigation
            const currentPage = window.location.pathname.split('/').pop() || 'index.php';
            
            // Desktop navigation highlighting
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                const href = link.getAttribute('href');
                if (href && (href === currentPage || href.includes(currentPage.replace('.php', '')))) {
                    link.classList.add('active');
                    link.style.color = '#22c55e';
                    link.style.fontWeight = '600';
                    link.style.position = 'relative';
                    // Add green underline indicator
                    link.style.borderBottom = '2px solid #22c55e';
                }
            });
            
            // Mobile footer highlighting
            document.querySelectorAll('.mobile-nav-item').forEach(item => {
                const href = item.getAttribute('href');
                if (href && href.includes(currentPage.replace('.php', ''))) {
                    item.classList.add('current-page');
                }
            });

            // Add haptic feedback simulation for mobile
            if ('vibrate' in navigator) {
                document.querySelectorAll('.mobile-nav-item, .nav-link, .mobile-close-btn').forEach(element => {
                    element.addEventListener('click', function() {
                        navigator.vibrate(50); // Short vibration for app-like feel
                    });
                });
            }

            // Add swipe gesture support for mobile menu
            let startY = 0;
            let startX = 0;

            navbarCollapse.addEventListener('touchstart', function(e) {
                startY = e.touches[0].clientY;
                startX = e.touches[0].clientX;
            });

            navbarCollapse.addEventListener('touchend', function(e) {
                const endY = e.changedTouches[0].clientY;
                const endX = e.changedTouches[0].clientX;
                const diffY = startY - endY;
                const diffX = Math.abs(startX - endX);

                // Swipe up to close menu
                if (diffY > 50 && diffX < 100) {
                    const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                        hide: true
                    });
                }
            });
        });
    </script>
