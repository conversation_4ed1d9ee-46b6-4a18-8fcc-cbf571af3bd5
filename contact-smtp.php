<?php
/**
 * SMTP Contact Form Handler for Eldergrove Energy
 * Uses PHPMailer for reliable email delivery via SMTP
 * 
 * To use this file:
 * 1. Install PHPMailer: composer require phpmailer/phpmailer
 * 2. Configure SMTP settings below
 * 3. Rename this file to contact.php (backup the original first)
 */

// Include PHPMailer
require_once 'vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use P<PERSON>Mailer\PHPMailer\Exception;

// SMTP Configuration - UPDATE THESE SETTINGS
$smtp_config = [
    'host' => 'smtp.hostinger.com', // Hostinger SMTP server
    'port' => 587, // or 465 for SSL
    'encryption' => 'tls', // or 'ssl'
    'username' => '<EMAIL>', // Your email
    'password' => 'YOUR_EMAIL_PASSWORD', // Your email password
    'from_email' => '<EMAIL>',
    'from_name' => 'Eldergrove Energy',
    'to_email' => '<EMAIL>',
    'reply_to_email' => '<EMAIL>'
];

// General Configuration
$config = [
    'log_submissions' => true,
    'enable_debug' => false // Set to true for development
];

// Enable error reporting for development only
if ($config['enable_debug']) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set content type
header('Content-Type: application/json');

// Allow CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Response array
$response = array();

// Check if form was submitted via POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    // Sanitize and validate input data
    $name = isset($_POST['name']) ? trim(htmlspecialchars($_POST['name'], ENT_QUOTES, 'UTF-8')) : '';
    $email = isset($_POST['email']) ? trim(strtolower($_POST['email'])) : '';
    $company = isset($_POST['company']) ? trim(htmlspecialchars($_POST['company'], ENT_QUOTES, 'UTF-8')) : '';
    $subject = isset($_POST['subject']) ? trim(htmlspecialchars($_POST['subject'], ENT_QUOTES, 'UTF-8')) : '';
    $message = isset($_POST['message']) ? trim(htmlspecialchars($_POST['message'], ENT_QUOTES, 'UTF-8')) : '';
    
    // Validation (same as before)
    $errors = array();
    
    if (empty($name)) {
        $errors[] = 'Name is required';
    } elseif (strlen($name) < 2) {
        $errors[] = 'Name must be at least 2 characters long';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }
    
    if (empty($subject)) {
        $errors[] = 'Service interest is required';
    }
    
    if (empty($message)) {
        $errors[] = 'Message is required';
    } elseif (strlen($message) < 10) {
        $errors[] = 'Message must be at least 10 characters long';
    }
    
    // Anti-spam measures
    $honeypot = isset($_POST['website']) ? $_POST['website'] : '';
    if (!empty($honeypot)) {
        $errors[] = 'Spam detected';
    }
    
    // Rate limiting
    session_start();
    $current_time = time();
    $last_submission = isset($_SESSION['last_contact_submission']) ? $_SESSION['last_contact_submission'] : 0;
    
    if (($current_time - $last_submission) < 60) {
        $errors[] = 'Please wait before sending another message';
    }
    
    // If no errors, send email
    if (empty($errors)) {
        $_SESSION['last_contact_submission'] = $current_time;
        
        $mail = new PHPMailer(true);
        
        try {
            // Server settings
            $mail->isSMTP();
            $mail->Host = $smtp_config['host'];
            $mail->SMTPAuth = true;
            $mail->Username = $smtp_config['username'];
            $mail->Password = $smtp_config['password'];
            $mail->SMTPSecure = $smtp_config['encryption'];
            $mail->Port = $smtp_config['port'];
            
            // Recipients
            $mail->setFrom($smtp_config['from_email'], $smtp_config['from_name']);
            $mail->addAddress($smtp_config['to_email']);
            $mail->addReplyTo($email, $name);
            
            // Content
            $mail->isHTML(true);
            $mail->Subject = 'New Contact Form Submission: ' . $subject;
            
            // HTML body
            $mail->Body = "
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='UTF-8'>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
                    .content { background: #f8fafc; padding: 20px; border-radius: 0 0 8px 8px; }
                    .field { margin-bottom: 15px; }
                    .label { font-weight: bold; color: #1e293b; }
                    .value { margin-top: 5px; padding: 10px; background: white; border-radius: 4px; border-left: 4px solid #22c55e; }
                    .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0; font-size: 12px; color: #64748b; }
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h2>New Contact Form Submission</h2>
                        <p>Eldergrove Energy Website</p>
                    </div>
                    <div class='content'>
                        <div class='field'>
                            <div class='label'>Name:</div>
                            <div class='value'>" . htmlspecialchars($name) . "</div>
                        </div>
                        <div class='field'>
                            <div class='label'>Email:</div>
                            <div class='value'>" . htmlspecialchars($email) . "</div>
                        </div>
                        " . (!empty($company) ? "
                        <div class='field'>
                            <div class='label'>Company:</div>
                            <div class='value'>" . htmlspecialchars($company) . "</div>
                        </div>
                        " : "") . "
                        <div class='field'>
                            <div class='label'>Service Interest:</div>
                            <div class='value'>" . htmlspecialchars($subject) . "</div>
                        </div>
                        <div class='field'>
                            <div class='label'>Message:</div>
                            <div class='value'>" . nl2br(htmlspecialchars($message)) . "</div>
                        </div>
                        <div class='footer'>
                            <p><strong>Submission Details:</strong></p>
                            <p>Date: " . date('Y-m-d H:i:s T') . "</p>
                            <p>IP Address: " . $_SERVER['REMOTE_ADDR'] . "</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ";
            
            // Plain text version
            $mail->AltBody = "
New contact form submission from Eldergrove Energy website:

Name: $name
Email: $email" . (!empty($company) ? "\nCompany: $company" : "") . "
Service Interest: $subject

Message:
$message

---
Submitted on: " . date('Y-m-d H:i:s T') . "
IP Address: " . $_SERVER['REMOTE_ADDR'] . "
            ";
            
            $mail->send();
            
            // Log successful submission
            if ($config['log_submissions']) {
                $log_entry = date('Y-m-d H:i:s') . " - SUCCESS - SMTP - Contact form submission from: $name ($email) - Subject: $subject\n";
                file_put_contents('contact_log.txt', $log_entry, FILE_APPEND | LOCK_EX);
            }
            
            $response['success'] = true;
            $response['message'] = 'Thank you for your message! We\'ll get back to you within 24 hours.';
            
        } catch (Exception $e) {
            // Log failed submission
            if ($config['log_submissions']) {
                $log_entry = date('Y-m-d H:i:s') . " - FAILED - SMTP - Error: {$mail->ErrorInfo} - From: $name ($email)\n";
                file_put_contents('contact_log.txt', $log_entry, FILE_APPEND | LOCK_EX);
            }
            
            $response['success'] = false;
            $response['message'] = 'Sorry, there was an error sending your message. Please try again later or contact us directly at ' . $smtp_config['to_email'];
            
            if ($config['enable_debug']) {
                $response['debug'] = $mail->ErrorInfo;
            }
        }
        
    } else {
        $response['success'] = false;
        $response['message'] = 'Please correct the following errors:';
        $response['errors'] = $errors;
    }
    
} else {
    $response['success'] = false;
    $response['message'] = 'Invalid request method';
}

// Return JSON response
echo json_encode($response);
?>
