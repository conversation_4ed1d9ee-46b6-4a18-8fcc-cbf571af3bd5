const { test, expect } = require('@playwright/test');

test.describe('Elder Grove Energy Website Comprehensive Audit', () => {
  const baseURL = 'http://localhost/eldergroveenergy.com';
  
  const pages = [
    { name: 'Homepage', url: `${baseURL}/index.php` },
    { name: 'About', url: `${baseURL}/about.php` },
    { name: 'Services', url: `${baseURL}/services.php` },
    { name: 'Projects', url: `${baseURL}/projects.php` },
    { name: 'Team', url: `${baseURL}/team.php` },
    { name: 'Technology', url: `${baseURL}/technology.php` },
    { name: 'Sustainability', url: `${baseURL}/sustainability.php` },
    { name: 'Careers', url: `${baseURL}/careers.php` },
    { name: 'Contact', url: `${baseURL}/contact.php` }
  ];

  test('Complete Website Audit', async ({ page }) => {
    console.log('\n🔍 ELDER GROVE ENERGY - COMPREHENSIVE WEBSITE AUDIT');
    console.log('=' + '='.repeat(60));
    
    const auditResults = [];
    
    for (const pageInfo of pages) {
      console.log(`\n📄 AUDITING: ${pageInfo.name.toUpperCase()}`);
      console.log('-'.repeat(40));
      
      try {
        // Navigate to page
        const startTime = Date.now();
        await page.goto(pageInfo.url, { waitUntil: 'networkidle' });
        const loadTime = Date.now() - startTime;
        
        // Collect audit data
        const auditData = {
          page: pageInfo.name,
          url: pageInfo.url,
          loadTime: loadTime,
          title: await page.title(),
          
          // Navigation
          navLinks: await page.locator('nav a, .navbar a, header a').count(),
          
          // Content
          h1Count: await page.locator('h1').count(),
          h2Count: await page.locator('h2').count(),
          h3Count: await page.locator('h3').count(),
          
          // Images
          totalImages: await page.locator('img').count(),
          imagesWithAlt: await page.locator('img[alt]').count(),
          
          // Forms
          forms: await page.locator('form').count(),
          inputs: await page.locator('input, textarea, select').count(),
          
          // Links
          totalLinks: await page.locator('a').count(),
          externalLinks: await page.locator('a[href^="http"]').count(),
          
          // SEO
          metaDescription: await page.locator('meta[name="description"]').count(),
          metaKeywords: await page.locator('meta[name="keywords"]').count(),
          
          // Mobile test
          mobileResponsive: true
        };
        
        // Test mobile responsiveness
        await page.setViewportSize({ width: 375, height: 667 });
        await page.waitForTimeout(500);
        auditData.mobileResponsive = await page.isVisible('body');
        await page.setViewportSize({ width: 1920, height: 1080 });
        
        // Performance metrics
        const performanceMetrics = await page.evaluate(() => {
          const navigation = performance.getEntriesByType('navigation')[0];
          return {
            domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
            loadComplete: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0
          };
        });
        
        auditData.domContentLoaded = performanceMetrics.domContentLoaded;
        auditData.loadComplete = performanceMetrics.loadComplete;
        
        auditResults.push(auditData);
        
        // Display results
        console.log(`✅ Status: Successfully loaded`);
        console.log(`⏱️  Load Time: ${loadTime}ms`);
        console.log(`📝 Page Title: "${auditData.title}"`);
        console.log(`🧭 Navigation: ${auditData.navLinks} links`);
        console.log(`📋 Content: H1(${auditData.h1Count}) H2(${auditData.h2Count}) H3(${auditData.h3Count})`);
        console.log(`🖼️  Images: ${auditData.totalImages} total, ${auditData.imagesWithAlt} with alt text`);
        console.log(`📋 Forms: ${auditData.forms} forms, ${auditData.inputs} inputs`);
        console.log(`🔗 Links: ${auditData.totalLinks} total, ${auditData.externalLinks} external`);
        console.log(`📱 Mobile: ${auditData.mobileResponsive ? '✅ Responsive' : '❌ Issues detected'}`);
        console.log(`🔍 SEO: ${auditData.metaDescription ? '✅' : '❌'} Meta description, ${auditData.metaKeywords ? '✅' : '❌'} Keywords`);
        console.log(`⚡ Performance: DOM(${auditData.domContentLoaded.toFixed(1)}ms) Load(${auditData.loadComplete.toFixed(1)}ms)`);
        
        // Issues and recommendations
        const issues = [];
        if (auditData.h1Count === 0) issues.push('Missing H1 heading');
        if (auditData.h1Count > 1) issues.push('Multiple H1 headings');
        if (auditData.imagesWithAlt < auditData.totalImages) issues.push(`${auditData.totalImages - auditData.imagesWithAlt} images missing alt text`);
        if (auditData.metaDescription === 0) issues.push('Missing meta description');
        if (loadTime > 3000) issues.push('Slow loading time');
        if (!auditData.mobileResponsive) issues.push('Mobile responsiveness issues');
        
        if (issues.length > 0) {
          console.log(`⚠️  Issues found:`);
          issues.forEach(issue => console.log(`   • ${issue}`));
        } else {
          console.log(`✅ No major issues detected`);
        }
        
      } catch (error) {
        console.log(`❌ Error loading page: ${error.message}`);
        auditResults.push({
          page: pageInfo.name,
          url: pageInfo.url,
          error: error.message
        });
      }
    }
    
    // Summary Report
    console.log('\n📊 AUDIT SUMMARY REPORT');
    console.log('=' + '='.repeat(60));
    
    const successfulPages = auditResults.filter(r => !r.error);
    const avgLoadTime = successfulPages.reduce((sum, p) => sum + p.loadTime, 0) / successfulPages.length;
    const totalImages = successfulPages.reduce((sum, p) => sum + (p.totalImages || 0), 0);
    const imagesWithoutAlt = successfulPages.reduce((sum, p) => sum + ((p.totalImages || 0) - (p.imagesWithAlt || 0)), 0);
    const pagesWithoutMetaDesc = successfulPages.filter(p => p.metaDescription === 0).length;
    const mobileIssues = successfulPages.filter(p => !p.mobileResponsive).length;
    
    console.log(`📄 Pages Tested: ${auditResults.length}`);
    console.log(`✅ Successful: ${successfulPages.length}`);
    console.log(`❌ Errors: ${auditResults.length - successfulPages.length}`);
    console.log(`⏱️  Average Load Time: ${avgLoadTime.toFixed(0)}ms`);
    console.log(`🖼️  Total Images: ${totalImages}, Missing Alt: ${imagesWithoutAlt}`);
    console.log(`🔍 Pages Missing Meta Description: ${pagesWithoutMetaDesc}`);
    console.log(`📱 Pages with Mobile Issues: ${mobileIssues}`);
    
    // Recommendations
    console.log('\n🎯 RECOMMENDATIONS');
    console.log('-'.repeat(30));
    
    if (avgLoadTime > 2000) {
      console.log('• Optimize page loading times (target: <2s)');
    }
    if (imagesWithoutAlt > 0) {
      console.log('• Add alt text to all images for accessibility');
    }
    if (pagesWithoutMetaDesc > 0) {
      console.log('• Add meta descriptions to all pages for SEO');
    }
    if (mobileIssues > 0) {
      console.log('• Fix mobile responsiveness issues');
    }
    
    console.log('• Consider adding structured data for better SEO');
    console.log('• Implement lazy loading for images');
    console.log('• Add breadcrumb navigation');
    console.log('• Consider adding a search functionality');
    
    console.log('\n✅ AUDIT COMPLETE');
  });
});
