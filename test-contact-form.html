<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Form Test - Eldergrove Energy</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1e293b;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #22c55e;
        }
        
        .test-header h1 {
            color: #22c55e;
            margin-bottom: 0.5rem;
        }
        
        .test-info {
            background: #f0f9ff;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            border-left: 4px solid #3b82f6;
        }
        
        .contact-form-wrapper-modern {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid #f1f5f9;
        }
        
        .contact-form-modern .form-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .contact-form-modern .form-group-half {
            flex: 1;
        }
        
        .contact-form-modern .form-group {
            margin-bottom: 1rem;
        }
        
        .contact-form-modern label {
            display: block;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .contact-form-modern input,
        .contact-form-modern select,
        .contact-form-modern textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: white;
            box-sizing: border-box;
        }
        
        .contact-form-modern input:focus,
        .contact-form-modern select:focus,
        .contact-form-modern textarea:focus {
            outline: none;
            border-color: #22c55e;
            box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
        }
        
        .btn-contact-submit-modern {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 0.875rem 1.75rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
        }
        
        .btn-contact-submit-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
        }
        
        .btn-contact-submit-modern:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none !important;
        }
        
        /* Form Message Styles */
        .form-message {
            margin: 1.5rem 0;
            padding: 1.5rem;
            border-radius: 12px;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            animation: slideIn 0.3s ease-out;
            transition: opacity 0.3s ease;
        }
        
        .form-success {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            border: 1px solid #22c55e;
            color: #15803d;
        }
        
        .form-error {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            border: 1px solid #ef4444;
            color: #dc2626;
        }
        
        .success-icon, .error-icon {
            font-size: 1.5rem;
            flex-shrink: 0;
            margin-top: 0.25rem;
        }
        
        .success-icon i {
            color: #22c55e;
        }
        
        .error-icon i {
            color: #ef4444;
        }
        
        .success-content h4, .error-content h4 {
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .success-content p, .error-content p {
            margin: 0;
            line-height: 1.5;
        }
        
        .error-content ul {
            margin: 0.5rem 0 0 0;
            padding-left: 1.25rem;
        }
        
        .error-content li {
            margin-bottom: 0.25rem;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .debug-info {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 2rem;
            font-family: monospace;
            font-size: 0.85rem;
        }
        
        @media (max-width: 768px) {
            .contact-form-modern .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-envelope"></i> Contact Form Test</h1>
            <p>Eldergrove Energy - Email Testing</p>
        </div>
        
        <div class="test-info">
            <h3><i class="fas fa-info-circle"></i> Test Information</h3>
            <p><strong>Main Email:</strong> <EMAIL> (receives all contact form submissions)</p>
            <p><strong>Test Email:</strong> <EMAIL> (for testing purposes)</p>
            <p><strong>Form Handler:</strong> contact.php (enhanced with debugging enabled)</p>
            <p><strong>Debug Mode:</strong> Enabled for testing</p>
        </div>
        
        <div class="contact-form-wrapper-modern">
            <form class="contact-form-modern" action="contact.php" method="POST">
                <div class="form-row">
                    <div class="form-group-half">
                        <label for="name">Full Name *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-group-half">
                        <label for="email">Email Address *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group-half">
                        <label for="company">Company</label>
                        <input type="text" id="company" name="company">
                    </div>
                    <div class="form-group-half">
                        <label for="subject">Service Interest *</label>
                        <select id="subject" name="subject" required>
                            <option value="">Select service</option>
                            <option value="Wind Energy">Wind Energy</option>
                            <option value="Solar Power">Solar Power</option>
                            <option value="Energy Storage">Energy Storage</option>
                            <option value="General Inquiry">General Inquiry</option>
                            <option value="Test Submission">Test Submission</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="message">Message *</label>
                    <textarea id="message" name="message" rows="4" required placeholder="This is a test message for the contact form..."></textarea>
                </div>
                <!-- Honeypot field for spam protection -->
                <input type="text" name="website" style="display: none;">
                <div class="form-group">
                    <button type="submit" class="btn-contact-submit-modern">
                        <span>Send Test Message</span>
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </form>
        </div>
        
        <div class="debug-info">
            <h4>Debug Console:</h4>
            <div id="debug-output">Open browser console (F12) to see detailed debugging information...</div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script>
        // Additional debugging for the test page
        console.log('Contact form test page loaded');
        console.log('Current URL:', window.location.href);
        
        // Log when forms are found
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form[action="contact.php"]');
            console.log('Test page - Found forms:', forms.length);
            
            const debugOutput = document.getElementById('debug-output');
            debugOutput.innerHTML = `
                Found ${forms.length} contact form(s)<br>
                JavaScript loaded: ${typeof initializeContactForms !== 'undefined' ? 'Yes' : 'No'}<br>
                Current time: ${new Date().toLocaleString()}
            `;
        });
    </script>
</body>
</html>
