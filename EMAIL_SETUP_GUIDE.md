# Email Setup Guide for Eldergrove Energy Contact Form

## Current Setup ✅

Your contact form is now enhanced and ready to use with **PHP mail()** function, which should work on Hostinger hosting.

### What's Been Improved:

1. **Enhanced PHP Contact Handler** (`contact.php`)
   - Better validation and security
   - HTML email formatting
   - Rate limiting (1 minute between submissions)
   - Spam protection (honeypot)
   - Detailed logging
   - Professional email templates

2. **JavaScript Form Handling** (`assets/js/main.js`)
   - AJAX form submission
   - Loading states
   - Success/error messages
   - Form validation feedback

3. **CSS Styling** (`contact-page.php`)
   - Beautiful success/error message styling
   - Mobile-responsive design

## Testing Your Current Setup

1. **Test the form** on your website
2. **Check if emails arrive** at <EMAIL>
3. **Check the log file** `contact_log.txt` for submission records

## If PHP mail() Doesn't Work (SMTP Alternative)

### Option 1: Install PHPMailer for SMTP

```bash
# In your project directory, run:
composer require phpmailer/phpmailer
```

### Option 2: Manual PHPMailer Installation

1. Download PHPMailer from: https://github.com/PHPMailer/PHPMailer
2. Extract to a `vendor/phpmailer/` folder in your project
3. Update the require path in `contact-smtp.php`

### Hostinger SMTP Settings

Update `contact-smtp.php` with these Hostinger SMTP settings:

```php
$smtp_config = [
    'host' => 'smtp.hostinger.com',
    'port' => 587, // or 465 for SSL
    'encryption' => 'tls', // or 'ssl' for port 465
    'username' => '<EMAIL>',
    'password' => 'YOUR_EMAIL_PASSWORD',
    'from_email' => '<EMAIL>',
    'from_name' => 'Eldergrove Energy',
    'to_email' => '<EMAIL>'
];
```

### To Switch to SMTP:

1. **Backup current contact.php**: `cp contact.php contact-php-mail-backup.php`
2. **Use SMTP version**: `cp contact-smtp.php contact.php`
3. **Update SMTP credentials** in the new contact.php file

## Hostinger Email Configuration

### Check Your Email Settings:

1. **Log into Hostinger control panel**
2. **Go to Email section**
3. **Verify your email account exists**: <EMAIL>
4. **Note the SMTP settings** provided by Hostinger

### Common Hostinger SMTP Settings:

- **Incoming Server (IMAP)**: imap.hostinger.com (Port: 993, SSL)
- **Outgoing Server (SMTP)**: smtp.hostinger.com (Port: 587, TLS or Port: 465, SSL)

## Troubleshooting

### If Emails Don't Send:

1. **Check PHP mail() is enabled**:
   ```php
   <?php
   if (function_exists('mail')) {
       echo "mail() function is available";
   } else {
       echo "mail() function is NOT available";
   }
   ?>
   ```

2. **Check server logs** in Hostinger control panel
3. **Verify MX records** for your domain
4. **Test with a simple email**:
   ```php
   <?php
   $result = mail('<EMAIL>', 'Test', 'Test message', 'From: <EMAIL>');
   echo $result ? 'Email sent' : 'Email failed';
   ?>
   ```

### If Using SMTP:

1. **Verify SMTP credentials** are correct
2. **Check firewall/port restrictions**
3. **Enable debug mode** in `contact-smtp.php`:
   ```php
   $config['enable_debug'] = true;
   ```

## Security Recommendations

1. **Set proper file permissions**:
   - `contact.php`: 644
   - `contact_log.txt`: 644 (will be created automatically)

2. **For production, disable debug mode**:
   ```php
   $config['enable_debug'] = false;
   ```

3. **Consider adding CAPTCHA** for additional spam protection

## File Structure

```
your-website/
├── contact.php (Enhanced PHP mail version)
├── contact-smtp.php (SMTP version - backup)
├── contact-page.php (Updated with new styles)
├── assets/js/main.js (Updated with form handling)
├── contact_log.txt (Auto-created for logging)
└── EMAIL_SETUP_GUIDE.md (This file)
```

## Next Steps

1. **Test your current setup** first
2. **If emails don't arrive**, switch to SMTP method
3. **Monitor the contact_log.txt** file for submissions
4. **Consider adding email notifications** for new submissions

## Support

If you need help:
1. Check Hostinger documentation for email settings
2. Test with simple PHP mail() first
3. Use SMTP as a fallback option
4. Monitor server logs for any errors

Your contact form is now production-ready with professional styling and robust error handling! 🚀
